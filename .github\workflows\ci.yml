name: CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: example
          MYSQL_DATABASE: crm
          MYSQL_USER: crmuser
          MYSQL_PASSWORD: crmpass
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v2

    - name: Set up PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '7.4'
        extensions: mysqli, mbstring, gd

    - name: Import test database
      run: mysql -h 127.0.0.1 -u root -pexample crm < minimal_db.sql

    - name: Run tests
      run: |
        # Add your test commands here
        echo "Tests would run here"
        # Example: php run-tests.php
