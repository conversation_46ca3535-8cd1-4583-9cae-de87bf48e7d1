FROM php:7.4-apache

# Install system packages and PHP extensions required by the CRM
RUN apt-get update && apt-get install -y \
    libpng-dev libjpeg-dev libfreetype6-dev \
    libonig-dev libxml2-dev libcurl4-openssl-dev \
    libzip-dev libicu-dev \
    git unzip curl \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install mysqli pdo pdo_mysql mbstring exif pcntl bcmath gd \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Node.js 18.x (LTS)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Set the working directory
WORKDIR /var/www/html

# Copy application code
COPY . /var/www/html

# phpMyAdmin has been removed from the codebase

# Update jQuery clueTip with secure dependencies
# Instead of downloading from GitHub (which seems to have issues), we'll just update the package.json
RUN mkdir -p /var/www/html/_lib/js/cluetip

# Update package.json with secure dependencies
RUN cd /var/www/html/_lib/js/cluetip/ && \
    echo '{\
  "name": "cluetip",\
  "title": "jQuery clueTip plugin",\
  "version": "1.2.14",\
  "description": "Displays a highly customizable tooltip when the user interacts with the matched element.",\
  "main": "jquery.cluetip.js",\
  "devDependencies": {\
    "grunt": "^1.5.3",\
    "grunt-contrib-jshint": "^3.2.0",\
    "grunt-contrib-uglify": "^5.2.2",\
    "grunt-contrib-concat": "^2.1.0",\
    "grunt-shell": "^4.0.0",\
    "grunt-version": "^3.0.0"\
  },\
  "repository": {\
    "type": "git",\
    "url": "git://github.com/kswedberg/jquery-cluetip.git"\
  },\
  "keywords": [\
    "jQuery",\
    "plugin",\
    "tooltip"\
  ],\
  "author": {\
    "name": "Karl Swedberg",\
    "email": "<EMAIL>"\
  },\
  "licenses": [\
    {\
      "type": "MIT",\
      "url": "http://www.opensource.org/licenses/mit-license.php"\
    }\
  ],\
  "readmeFilename": "readme.md"\
}' > package.json

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html

# Expose port 80
EXPOSE 80

# Start Apache in the foreground
CMD ["apache2-foreground"]
