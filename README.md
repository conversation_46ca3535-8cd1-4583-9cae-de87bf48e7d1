# CRM Business Project

This is a PHP-based CRM (Customer Relationship Management) solution for managing client relationships, document generation, and business communications.

## Overview

This CRM application provides:
- Client profile and contact management
- Document generation with company-specific templates
- Email sending capabilities using PHPMailer
- Client notes and history tracking
- User authentication and admin tools
- Message sending and tracking functionality
- Client shares and referrals management

## Technologies

- Built with PHP (backend)
- MySQL/MariaDB database
- jQuery and jQuery UI for user interfaces
- CKEditor for rich text editing in forms
- PHPMailer for email communications
- Custom document templating system

## Dependencies

This project requires a standard PHP stack. Key prerequisites include:

- PHP 7.4 or newer with common extensions (`mysqli`, `imap`, `mbstring`, `curl`, `gd`)
- A MySQL or MariaDB database
- wkhtmltopdf for PDF generation

See [DEPENDENCIES.md](DEPENDENCIES.md) for the complete list and setup instructions.

## Project Structure

- `_lib/`: Core libraries and dependencies
  - `_lib/js/`: JavaScript libraries (jQuery, CKEditor, etc.)
  - `_lib/mailer/`: PHPMailer components
  - `_lib/calendar/`: Date picker components

- `controllers/`: Application logic organized by function
  - `controllers/ajax/`: AJAX endpoints for dynamic functionality
  - `controllers/api/`: API endpoints for external access
  - `controllers/client/`: Client management functions
  - `controllers/tools/`: Administrative tools

- `documents/`: Document system
  - `documents/content/`: Document content templates
  - `documents/templates/`: Company-specific document templates (large collection of company templates)
  - `documents/refs/`: Reference files and images

## Setup

1. Clone or download the repository
2. Set up a web server (Apache/Nginx) with PHP support
3. Create a MySQL/MariaDB database
4. Provide database and mail credentials via environment variables (see
   `_lib/config.inc.php` for the list of variables)
5. Ensure the web server has write permissions for document generation

## Docker Deployment with Nginx and MySQL

The repository includes a `Dockerfile` for the web application and a
`docker-compose.yml` that also provisions a MySQL database. All PHP, Node, and
composer dependencies are installed during the image build so the container is
self-contained. This is useful for local development or for running the CRM in
container orchestration platforms.

Start the services (building the image if necessary) with:

```bash
docker compose up --build
```

The web application will be available on `http://localhost:8080`. The MySQL
server runs in the `db` service with credentials defined in the compose file.
Modify these environment variables as needed for your setup.

## Security Notice

This application handles business communication and potentially sensitive client data. For production use:

- Use HTTPS for all connections
- Implement proper user access controls
- Regularly backup the database
- Keep PHP and all libraries updated with security patches
- Consider security auditing before deployment
