<?php
	class DB
	{
		function __construct()
		{
			define ("DB_DATE", 1);
			define ("DB_VARCHAR", 2);
			define ("DB_INT4", 3);
			define ("DB_BPCHAR", 4);
			define ("DB_FLOAT4", 4);
			define ("DB_UNKNOWN", 100);
		}

		function prepare_insert($arr, $tbl)
		{
			if(is_array($arr) && $tbl!='')
			{
				$fields = '';
				$values = '';
				foreach($arr as $f => $v )
				{
					$fields  .= "$f ,";
					$values .= "'".$this->real_escape_string($v)."' ,";
				}
				$fields = substr($fields,0,-1);
				$values = substr($values,0,-1);
				$insert_query = "INSERT INTO $tbl($fields) VALUES($values)";

				return $insert_query;
			}
			else
				return false;
		}

		function prepare_update($arr, $tbl, $condition='')
		{
			if(is_array($arr) && !empty($tbl))
			{
				$sql = '';
				foreach($arr as $f => $v)
				{
					$sql  .= " $f = '".$this->real_escape_string($v)."',";
				}
				$sql = substr($sql,0,-1);
				$update_query = "UPDATE $tbl SET $sql $condition";

				return $update_query;
			}
			else
				return false;
		}

		function select_values($id_dbField, $val_dbField, $tbl, $selected='', $cond='', $extra='')
		{
			$str = '';
			$rs = $this->query( "SELECT $id_dbField, $val_dbField FROM $tbl $cond" );
			while( $r = $this->fetch_object($rs))
			{
				if(strpos($id_dbField, ' as '))
				{
					$field_parts = explode(' as ', $id_dbField);
					$id_dbField = $field_parts[1];
				}
				$id = $r->{$id_dbField};
				$val = $r->{$val_dbField};

				$isSelected = ($id == $selected) ? 'selected' : '';

				$str .= "<option value='$id' $extra $isSelected>$val</option>\n";
			}
			return $str;
		}

		function checkUser($table, $f_user, $f_pass, $user, $pass, $fields = '*')
		{
			$user = $this->real_escape_string($user);
			$pass = $this->real_escape_string($pass);

			$query = "SELECT $fields FROM $table WHERE $f_user = '$user' AND $f_pass = '$pass'";

			$exists = $this->num_rows( $r = $this->query($query) );
			return ($exists===1) ? $this->fetch_object($r) : false;
		}

                function getQueryList($query)
                {
                        $rs = $this->query($query);
                        $list = array();
                        while( $r = $this->fetch_object($rs) ) {
                                $list[] = $r;
                        }
                        return $list;
                }

		function getAlias($id_user, $id_company)
		{
			$id_user = intval($id_user);
			$id_company = intval($id_company);

			$query = "SELECT name, title FROM company_employees WHERE id_user = $id_user AND id_company = $id_company AND is_active = 1";
			$r_alias = $this->fetch_object($this->query($query));

			$query = "SELECT username FROM users WHERE id = $id_user";
			$r_user = $this->fetch_object($this->query($query));

			//$query = "SELECT ce.name, title FROM company_employees ce, users u WHERE ce.id_user = $id_user AND ce.id_company = $id_company AND u.id = $id_user";

			if(isset($r_alias->title) && strlen($r_alias->title)) $r_alias->name = "{$r_alias->title}. $r_alias->name ";

			if(isset($r_user->username)) $r_alias->name .= "($r_user->username)";
			return $r_alias;
		}



		function masterNeeded($query)
		{
			$query_types = array('insert','update','delete');
			foreach($query_types as $type)
			{
				$pos = strpos(strtolower($query),$type);

				if ($pos === 0)	return true;
			}
			return false;
		}

	};


	class MySQL extends DB
	{
		var $conn;

		function connect()
		{
			global $db_user, $db_pass, $db_name, $db_host;
			$this->conn = mysqli_connect($db_host, $db_user, $db_pass, $db_name) or die(mysqli_error());
			return $this->conn;
		}

		function query($query)
		{
			$result = mysqli_query($this->conn, $query) or die("<b>Error:</b><br>$query<br>".$this->last_error());
			return $result;
		}

		function insert_id() { return mysqli_insert_id($this->conn); }

		function real_escape_string($value){ return mysqli_real_escape_string($this->conn, $value);	}

		function last_error(){  return mysqli_error($this->conn); }

		function execute($query){ return $this->query($query); }

		//function result($query,$row,$field){ return mysqli_result($query,$row,$field); }

		function result($res, $row, $field=0) {
			$res->data_seek($row);
			$datarow = $res->fetch_array();
			return $datarow[$field];
		}


		function num_rows($result){ return mysqli_num_rows($result); }

		function fetch_array($result) { return mysqli_fetch_array($result);	}

		function fetch_assoc($result) { return mysqli_fetch_assoc($result); }

		function fetch_object($result) { return mysqli_fetch_object($result); }

		function list_fields($table){
			// mysqli_list_fields was deprecated in PHP 5.4.0 and removed in PHP 7.0.0
			// Use SHOW COLUMNS instead
			return $this->query("SHOW COLUMNS FROM " . $this->real_escape_string($table));
		}

		function num_fields($fields){ return mysqli_num_fields($fields); }

		function field_name($fields, $index){ return mysqli_field_name($fields, $index);	}

		function field_flags($fields,$index){ return mysqli_field_flags($fields, $index); }

		function field_type($fields,$index){ return mysqli_field_type($fields, $index);	}

	};
?>
