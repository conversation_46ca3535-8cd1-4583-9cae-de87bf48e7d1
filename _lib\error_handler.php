<?php
/**
 * Enhanced <PERSON>rror Handler for LionCRM
 * Provides comprehensive error logging and security monitoring
 */

class ErrorHandler {
    private static $logFile;
    private static $securityLogFile;
    
    public static function init() {
        self::$logFile = __DIR__ . '/../logs/php_errors.log';
        self::$securityLogFile = __DIR__ . '/../logs/security.log';
        
        // Ensure log directory exists
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Set custom error handler
        set_error_handler([__CLASS__, 'handleError']);
        set_exception_handler([__CLASS__, 'handleException']);
        register_shutdown_function([__CLASS__, 'handleFatalError']);
    }
    
    public static function handleError($severity, $message, $file, $line) {
        $errorTypes = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_PARSE => 'PARSE',
            E_NOTICE => 'NOTICE',
            E_CORE_ERROR => 'CORE_ERROR',
            E_CORE_WARNING => 'CORE_WARNING',
            E_COMPILE_ERROR => 'COMPILE_ERROR',
            E_COMPILE_WARNING => 'COMPILE_WARNING',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE',
            E_STRICT => 'STRICT',
            E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
            E_DEPRECATED => 'DEPRECATED',
            E_USER_DEPRECATED => 'USER_DEPRECATED'
        ];
        
        $errorType = isset($errorTypes[$severity]) ? $errorTypes[$severity] : 'UNKNOWN';
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $uri = $_SERVER['REQUEST_URI'] ?? 'Unknown';
        
        $logMessage = sprintf(
            "[%s] %s: %s in %s on line %d | IP: %s | URI: %s | User-Agent: %s\n",
            $timestamp,
            $errorType,
            $message,
            $file,
            $line,
            $ip,
            $uri,
            $userAgent
        );
        
        error_log($logMessage, 3, self::$logFile);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    public static function handleException($exception) {
        $timestamp = date('Y-m-d H:i:s');
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $uri = $_SERVER['REQUEST_URI'] ?? 'Unknown';
        
        $logMessage = sprintf(
            "[%s] EXCEPTION: %s in %s on line %d | IP: %s | URI: %s | User-Agent: %s\nStack trace:\n%s\n",
            $timestamp,
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            $ip,
            $uri,
            $userAgent,
            $exception->getTraceAsString()
        );
        
        error_log($logMessage, 3, self::$logFile);
    }
    
    public static function handleFatalError() {
        $error = error_get_last();
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            self::handleError($error['type'], $error['message'], $error['file'], $error['line']);
        }
    }
    
    public static function logSecurityEvent($event, $details = []) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $uri = $_SERVER['REQUEST_URI'] ?? 'Unknown';
        $user = $_SESSION['crm_logged_in']['userinfo']->username ?? 'Anonymous';
        
        $logMessage = sprintf(
            "[%s] SECURITY: %s | User: %s | IP: %s | URI: %s | Details: %s | User-Agent: %s\n",
            $timestamp,
            $event,
            $user,
            $ip,
            $uri,
            json_encode($details),
            $userAgent
        );
        
        error_log($logMessage, 3, self::$securityLogFile);
    }
}

// Initialize error handler
ErrorHandler::init();
?>
