<?php

	$id_user = intval($_REQUEST['id_user']);
	$id_company = intval($_REQUEST['id_company']);

	if($id_user && $id_company)
	{
		$query = "SELECT e.*, p.name as position FROM company_employees e, positions p
					WHERE e.is_active = '1' AND e.id_user = $id_user AND e.id_company = $id_company AND e.id_position = p.id
					ORDER BY created_at DESC";

		$rs_agents = $db->query($query);

		if($db->num_rows($rs_agents))
		{


			$so_agents = '';
			while($agent = $db->fetch_object($rs_agents))
				$so_agents .= "<option value='" . intval($agent->id) . "'>" . htmlspecialchars($agent->name) . " (" . htmlspecialchars($agent->position) . ")</option>\n";

		}

		$so_agents = (!strlen($so_agents)) ? '<option>No agents for this user in this company.</option>' : '<option value=0>Select Agent...</option>'.$so_agents;
		echo $so_agents;
	}

?>