 <?php

	$id_message = intval($var2);
	if(!$id_message)
		exit();

	$query = "SELECT co.*, c.name as client_name, c.id_user_agent, c.id_user_registry
					FROM client_outbox co, clients c
					WHERE co.id_client = c.id AND co.id = " . $id_message;

	$rs = $db->query($query);
	$email = $db->fetch_object($rs);
	$email->date = date('Y-m-d H:i', strtotime($email->created_at));
	$email->attachs = ($email->attachs) ? unserialize($email->attachs) : '';
?>

<div class="message">
	<table>
		<tbody>
			<tr>
				<td style="width: 100px;"><strong>Date</strong></td>
				<td><?=htmlspecialchars($email->date)?></td>
			</tr>

			<tr>
				<td><strong>From</strong></td>
				<td><?=htmlspecialchars($email->email_from)?></td>
			</tr>

			<tr>
				<td><strong>To</strong></td>
				<td><?=htmlspecialchars($email->email_to)?> (<a href="<?=$base?>client/profile/<?=intval($email->id_client)?>"><?=intval($email->id_client)?> - <?=htmlspecialchars($email->client_name)?>)</a>)</td>
			</tr>

			<tr>
				<td><strong>Subject</strong></td>
				<td><?=htmlspecialchars($email->subject)?></td>
			</tr>

			<tr>
				<td valign="top"><strong>Message</strong></td>
				<td><p style="border: 1px solid #DDD; padding: 10px;"><?=nl2br(htmlspecialchars($email->message)); ?></p></td>
			</tr>

			<tr>
				<td><strong>Attachments</strong></td>
				<td>
					<?php if($email->attachs): ?>

						<?php foreach($email->attachs as $att_name): ?>

						- <a style="line-height: 25px;" target="_blank" href="<?=$base?>tmp_docs/<?=htmlspecialchars($att_name)?>"><?=htmlspecialchars($att_name)?></a> <br>

						<?php endforeach; ?>

					<?php else: ?>
						No attachments.
					<?php endif; ?>
				</td>
			</tr>


		</tbody>
	</table>
</div>