<?
	$id_client = $var2;
	$note = $_REQUEST['note'];


	if(strlen($note) && $id_client)
	{
		$fields['created_at'] = date('Y-m-d H:i:s');
		$fields['id_user'] = $current_user['userinfo']->id;
		$fields['id_client'] = $id_client;

		$new_value = $db->real_escape_string(urldecode($note));
		$fields['note'] = $new_value;

		$query = $db->prepare_insert($fields, 'client_notes');

		//echo $query;
		if($db->query($query)) echo 'Ok!';


		//Audit
		$fields_audit['event'] = 'create';
		$fields_audit['field'] = 'client note';
		$fields_audit['new_value'] = $new_value;
		$fields_audit['old_value'] = '--';
		$fields_audit['id_client'] = $id_client;
		$fields_audit['id_user'] = $current_user['userinfo']->id;
		$fields_audit['created_at'] = date('Y-m-d H:i:s');

		$query = $db->prepare_insert($fields_audit, 'client_audit');
		$db->query($query);


	}


?>