<?php

	echo ("<BR><BR><BR>-- Double Space<BR><BR><BR>");

	$query = "SELECT c.id, c.name, c.id_status, l.name as list_name, s.name as status_name FROM clients c, statuses s, lists l
						WHERE c.name LIKE '%  %' AND c.id_list = l.id AND c.id_status = s.id ORDER BY id";

	$rs = $db->query($query);
	$total = $db->num_rows($rs);

	while( $r = $db->fetch_object($rs) )
	{
//		printr($r);

		$new_name = $db->real_escape_string(str_replace('  ', ' ', $r->name));

		$query = "update clients set name = '$new_name' WHERE id = $r->id";
		echo "<Br>$query";
		if($db->query($query)) echo '...ok';
	}
	echo "<br><br>total: $total";

	echo ("<BR><BR><BR>-- Mr. on name<BR><BR><BR>");

	$query = "SELECT c.id, c.name, c.id_status, l.name as list_name, s.name as status_name FROM clients c, statuses s, lists l
						WHERE c.name LIKE 'mr%' AND c.id_list = l.id AND c.id_status = s.id ORDER BY id";

	$rs = $db->query($query);
	$total = $db->num_rows($rs);

	while( $r = $db->fetch_object($rs) )
	{
//		printr($r);

		$new_name = str_replace('MR. ', '', $r->name);
		$new_name = str_replace('Mr. ', '', $new_name);
		$new_name = str_replace('MR ', '', $new_name);
		$new_name = str_replace('Mr ', '', $new_name);
		$new_name = str_replace('Mrs. ', '', $new_name);
		$new_name = str_replace('MRS. ', '', $new_name);
		$new_name = str_replace('Mrs ', '', $new_name);
		$new_name = str_replace('MRS ', '', $new_name);


		$new_name = $db->real_escape_string($new_name);

		$query = "update clients set name = '$new_name' WHERE id = $r->id";
		echo "<Br>$query";
		if($db->query($query)) echo '...ok';
	}
	echo "<br><br>total: $total";

	echo ("<BR><BR><BR>finished.<BR><BR><BR>");

?>