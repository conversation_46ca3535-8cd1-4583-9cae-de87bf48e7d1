<?php

//	$client_id = 1420;
//	$client_id = 204;
//	$client_id = 469;
//	$client_id = 558;
//	$client_id = 517;
//	$client_id = 1471;
//	$client_id = 2214;
//	$client_id = 1255;
//	$client_id = 2142;
//	$client_id = 2378;
//	$client_id = 2035;
//	$client_id = 2127;
//	$client_id = 2604;
//	$client_id = 2543;
//	$client_id = 1348;
//	$client_id = 2433;
//	$client_id = 2536;
//	$client_id = 2063;


	$client_id = intval($_REQUEST['id_client']);
	if(!$client_id) {
		echo "Invalid client ID.";
		exit();
	}
	$client_info = $db->fetch_object( $db->query("SELECT * FROM clients WHERE id = " . $client_id) );
	$check_id = $client_info->id;

	$save['info'] = serialize($client_info);
	$save['notes'] = serializeClientInfo($db->query("SELECT * FROM client_notes WHERE id_client = " . $client_id));
	$save['contacts'] = serializeClientInfo($db->query("SELECT * FROM client_contacts WHERE id_client = " . $client_id));
	$save['shares'] = serializeClientInfo($db->query("SELECT * FROM client_shares WHERE id_client = " . $client_id));
	$save['audit'] = serializeClientInfo($db->query("SELECT * FROM client_audit WHERE id_client = " . $client_id));
	$save = serialize($save);

	if($check_id == $client_id)
	{

		$db->query("DELETE FROM clients WHERE id = " . $client_id);
//		$db->query("DELETE FROM client_audit WHERE id_client = " . $client_id);
		$db->query("DELETE FROM client_contacts WHERE id_client = " . $client_id);
		$db->query("DELETE FROM client_notes  WHERE id_client = " . $client_id);
		$db->query("DELETE FROM client_shares WHERE id_client = " . $client_id);


		//Audit
		$fields_audit['event'] = 'delete client';
		$fields_audit['field'] = 'all';
		$fields_audit['new_value'] = '--';
		$fields_audit['old_value'] = $save;
		$fields_audit['id_client'] = $client_id;
		$fields_audit['id_user'] = $current_user['userinfo']->id;
		$fields_audit['created_at'] = date('Y-m-d H:i:s');

		$query = $db->prepare_insert($fields_audit, 'client_audit');
		$db->query($query);

		echo("Client <strong>" . intval($client_id) . " - " . htmlspecialchars($client_info->name) . "</strong> removed.");

	}

	function serializeClientInfo($rs){
		global $db;

		while( $r = $db->fetch_object($rs))
			$info[] = serialize($r);

		return serialize($info);
	}

?>