<?php
	$id_list = 24;

	$file = @file_get_contents('temp/list.csv');

	//$lines = explode(PHP_EOL, $file);
	$lines = preg_split( '/\r\n|\r|\n/', $file);

	$line1 = array_shift($lines);
	$labels = explode(',',str_replace(' ', '', $line1));

	if(isset($lines) && is_array($lines) && count($lines) > 0)
	{

		$db->query("SET SESSION sql_mode='NO_AUTO_VALUE_ON_ZERO'");

		foreach($lines as $line)
		{
			if(strlen($line) > 50)
			{
				//echo $line;
				//$fields = explode(',', $line);
				$fields = str_getcsv($line);
				$info = array_combine($labels, $fields);
				$name = $db->real_escape_string(str_replace('  ', ' ', $info['name']));


				unset($id_client);
				$id_client = getClient($name);

				if($id_client)
				{
					$i++;

					echo '<br>'.$id_client.' '.$name;

					unset($shares);
					$shares = configShares($info, $id_client, 22);

					if(isset($shares) && is_array($shares) && count($shares) > 0)
					{

						echo "<br><br>{$id_client}.......... removing shares:";
						$query = "DELETE FROM client_shares WHERE id_client = $id_client";
						if($db->query($query)) echo 'removed.'; else echo 'fail.';

						echo('<br>Shares:');
						foreach($shares as $share)
						{
							$query = $db->prepare_insert($share, 'client_shares');
							echo('<br>-'.$query);
							$db->query($query);
						}

					}
					//stocks
				}
			}
		}

	}

	// config stocks
	function configShares($info, $id_client, $howmany)
	{
		global $db;

		for($i=1;$i<=$howmany;$i++)
		{
			if( $info["t{$i}stock"] )
			{

				$name = $info["t{$i}stock"];
				$shares = intval(str_replace(',','',$info["t{$i}share"]));
				$price = (strlen($info["t{$i}pps"])) ? str_replace(',','.',$info["t{$i}pps"]) : 0;
				$price = floatval(str_replace('$', '', $price));


				$db_record = array(
										 'name' => $name

										,'shares' => $shares
										,'price' =>  $price
										,'total_value' => $shares * $price

										,'id_client' => $id_client
										,'created_at'	=> date('Y-m-d H:i:s')
									);

				$records[] = $db_record;
			}

		}

		return $records;
	}

	function getClient($name)
	{
		global $db, $id_list;

		$value = $db->real_escape_string(trim($name));
		$id_list = intval($id_list);
		$query = "SELECT id FROM clients WHERE name LIKE \"%$value%\" AND id_list = $id_list";

		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$r = $db->fetch_object($rs);
			return $r->id;
		}
		else
			return false;

	}

?>