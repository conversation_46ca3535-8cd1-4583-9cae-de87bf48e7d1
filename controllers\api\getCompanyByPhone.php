<?php
	//api/getCompanyByPhone/xxxx
	//retorna nome da company/registry/reference ou not found

        $phone = $db->real_escape_string($var2);
        $found = false;

	if(strlen($phone))
	{
		$query = "SELECT * FROM companies WHERE (phone = '$phone') LIMIT 1";
		$rs = $db->query($query);

		if($db->num_rows($rs))
		{
			$found = true;

			$company = $db->fetch_object($rs);
			//printr($company);

			echo $company->name;
		}
		else
		{
			$query = "SELECT * FROM refs WHERE (phone = '$phone') LIMIT 1";
			$rs = $db->query($query);

			if($db->num_rows($rs))
			{
				$found = true;

				$ref = $db->fetch_object($rs);
				//printr($ref);

				echo $ref->name;
			}

		}

		if(!$found) echo 'not found';
	}

?>
