<?php
	$action = $var1;
	$id = $var2;
	$action = (empty($action))?'search':$action;


	switch($action)
	{
		case 'search':
		case 'profile':
		case 'contacts':
		case 'shares':
		case 'documents':
		case 'audit':
		case 'messages':
		case 'messages_sent':
		case 'message_form':

			$view = 'client/'.$action;

		break;

		case 'pdf-profile':

			$view = 'client/'.$action;
			$pdf = true;
			include("controllers/client/profile.php");

		break;


		default:

			$view = 'client/search';

		break;
	}


	if(is_file("controllers/client/$action.php"))
		include("controllers/client/$action.php");



?>
