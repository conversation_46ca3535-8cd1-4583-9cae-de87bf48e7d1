<?php
	$action = $var1;
	$id = intval($var2);
	$action = (empty($action))?'manage':$action;


	$subaction = $var3;
	$id_employee = $var4;

	$sent = $_REQUEST['sent'];
	$sent_employee = $_REQUEST['sent_employee'];

	switch($action)
	{
		case 'add':
		case 'edit':
		case 'view':

			$view = ($action == 'view') ? 'companies/view' : 'companies/form';


			if($sent)
			{

				$fields = $_REQUEST['fields'];

				if($action == 'add')
				{
					$fields['created_at'] = date('Y-m-d H:i:s');
					$query = $db->prepare_insert($fields, 'companies');
					if($db->query($query))
						header("location: {$base}companies/edit/".$db->insert_id());

				}

				elseif ( $action == 'edit')
				{
					$fields['updated_at'] = date('Y-m-d H:i:s');
					$query = $db->prepare_update($fields, 'companies', "WHERE id = $id");
					if($db->query($query))
						$msg = "Company/Registry updated successfully";

				}

			}


			if($id)
			{

				$company = $db->fetch_object($db->query("SELECT * FROM companies WHERE id = $id"));

				if($sent_employee)
				{

						$fields_employee = $_REQUEST['fields_employee'];

						if($subaction == 'addEmployee')
						{
							$fields_employee['created_at'] = date('Y-m-d H:i:s');
							$query = $db->prepare_insert($fields_employee, 'company_employees');
							if($db->query($query))
							{
								$msg = "Employee successfully registered.";
								$id_employee = $db->insert_id();
							}

						}

						elseif ( $subaction == 'editEmployee')
						{
							$fields_employee['updated_at'] = date('Y-m-d H:i:s');

							$query = $db->prepare_update($fields_employee, 'company_employees', "WHERE id = $id_employee");
							if($db->query($query))
								header("location: {$base}companies/edit/{$company->id}/addEmployee");

						}
				}

				if ( $subaction == 'deleteEmployee')
				{
					$has_leads = $db->num_rows( $db->query("SELECT id FROM clients WHERE id_employee = $id_employee OR id_employee_registry = $id_employee") );
					if(!$has_leads)
					{
						$query = "DELETE FROM company_employees WHERE id = $id_employee";
						if($db->query($query))
						{
							header("location: {$base}companies/edit/{$company->id}");
						}
					}
					else{ $msg = "You can't delete this agent until all his clients have been reassigned. ($has_leads clients left)"; }

				}


				$query = "SELECT e.*, u.username, p.name as position FROM company_employees e, users u, positions p
					WHERE
						e.id_company = $company->id AND e.id_user = u.id AND e.id_position = p.id
					ORDER BY
						name, position";

				$rs_company_employees = $db->query($query);

				while ( $company_employee = $db->fetch_object($rs_company_employees))
				{
					$employees[] = $company_employee;


					if($subaction == 'editEmployee' && $id_employee == $company_employee->id)
					{
						$employee = $company_employee;
					}
				}

				$so['positions'] = $db->select_values('id', 'name', 'positions', $employee->id_position, "WHERE company_type = '$company->company_type' ORDER BY name");
				$so['users'] = $db->select_values('id', 'username', 'users', $employee->id_user, "WHERE is_active = '1' ORDER BY username");
				$so['registries'] = $db->select_values('id', 'name', 'companies', $company->id_registry, "WHERE company_type = 'registry' AND is_active = '1' ORDER BY name");
			}

		break;


		//list
		default:

			$view = 'companies/manage';

			$filterSent = $_REQUEST['filterSent'];
			$filters = $_REQUEST['filters'];

			if($filterSent)
			{

				foreach($_REQUEST['filters'] as $filter_name => $filter):

					if(!empty($filter) || $filter==='0'):

						switch($filter_name):

							case 'name':
							{
								$filter_clause .= "AND name LIKE '%$filter%' ";
								break;
							}

							case 'is_active':
							{
								$filter_clause .= "AND is_active = '{$filter}' ";
								break;
							}

							default: $filter_clause = NULL; break;

						endswitch;

					endif;

				endforeach;
			}
			else $filter_clause .= "AND is_active = '1' ";


			$query = "SELECT * FROM companies WHERE id<>0 $filter_clause ORDER BY company_type, name";
			$rs_companies = $db->query($query);

			while ( $company = $db->fetch_object($rs_companies))
				$companies[$company->company_type][] = $company;


			$total_companies = isset($companies['company']) && is_array($companies['company']) ? count($companies['company']) : 0;
			$total_registries = isset($companies['registry']) && is_array($companies['registry']) ? count($companies['registry']) : 0;

		break;
	}

	//include("controllers/client/$action.php");




?>
