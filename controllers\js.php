<script type="text/javascript">

var base_url = "<?=$base?>";

$(document).ready(function() {

	//change leads
	<? if($action == 'change'):?>

		$("#doc_changeleads").tabs();

		$("#selectAll").click(function () {
			$('.leads').attr('checked', this.checked);
		});


		//set default
		$("#distribute-change-user").val(0);

		$('#distribute-change-user').change(function(){

			var data = { 'id_user': this.value };

			$('#distribute-change-company').html('<option>Loading...</option>');

			$.ajax(
			{
				type: "POST", url: '<?=$base?>ajax/getCompanies/', data: data, cache: false,
				success: function(message){  $('#distribute-change-company').html(message);	}

			});

		});

		$('#distribute-change-company').change(function(){

			var data = { 'id_company': this.value , 'id_user': $('#distribute-change-user').val() };

			$('#distribute-change-agent').html('<option>Loading...</option>');

			$.ajax(
			{
				type: "POST", url: '<?=$base?>ajax/getAgent/', data: data, cache: false,
				success: function(message){
					$('#distribute-change-agent').html(message);
				}

			});

		});

		$('#distributeLeads').submit(function(){

			var id_user = $('#distribute-change-user').val();
			var id_company = $('#distribute-change-company').val();
			var id_employee = $('#distribute-change-agent').val();

			if(confirm('Are you sure?'))
			{

				var leads = '';
				$('.leads:checked').each(function(){

					leads = leads + this.value + ',';

				});
				leads = leads.substring(0, leads.length-1);

				$('#selectedLeads').val(leads);

			}

		});

		$("#update-status-select").val("");
		$('#updateStatusLeads').submit(function(){

			if(confirm('Are you sure?'))
			{

				var leads = '';
				$('.leads:checked').each(function(){

					leads = leads + this.value + ',';

				});
				leads = leads.substring(0, leads.length-1);

				$('#updateSelected').val(leads);

			}

		});

		$('#unassignLeads').submit(function(){

			if(confirm('Are you sure?'))
			{

				var leads = '';
				$('.leads:checked').each(function(){

					leads = leads + this.value + ',';

				});
				leads = leads.substring(0, leads.length-1);

				$('#unassignSelected').val(leads);

			}

		});

		$("#update-list-select").val("");
		$('#updateLeadsList').submit(function(){

			if(confirm('Are you sure?'))
			{

				var leads = '';
				$('.leads:checked').each(function(){

					leads = leads + this.value + ',';

				});
				leads = leads.substring(0, leads.length-1);

				$('#updateListSelected').val(leads);

			}

		});


	<? endif; //endif change leads ?>



	<? if( $interface == 'companies' && $action == 'manage'):?>

		$("#company_tabs").tabs();
		$("#company_tabs").bind( "tabsshow", function(event, ui) { window.location.hash = ui.panel.id;});

	<? endif; ?>

	<? if($action == 'documents'):?>



		//Update client spa sent date
		$("#spa_sent").change(function() {

			var spa_id = $("#spa_sent_id").val();
			var spa_date = $(this).val();

			var spa_data = "spa_id="+spa_id+"&spa_date="+spa_date;
			sendData("<?=$base?>ajax/update_spa_sent/", spa_data, "#msg_updating_spa", 'SPA Date');

		});


		//Update ipg date
		$("#share_ipg_date").change(function() {
			var share = "fields[ipg_date]=" + $(this).val();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#msg_updating_ipg_date", 'IPG Date');

		});



		//Update client margin on recertification deposit - open
		$("#share_margin_open").change(function() {
			var share = "fields[margin_open]=" + $(this).val();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#margin_open_info", 'Margin');

			$("#share_margin_company").html(100 - $(this).val());
		});


		//Update client margin on warrants - load
		$("#share_margin_warrants").change(function() {
			var share = "fields[margin_warrants]=" + $(this).val();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#margin_warrants_info", 'Margin');

		});


		//Update client warrrants
		$("#share_warrants").change(function() {
			var share = "fields[warrants]=" + $(this).val();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#warrants_info", 'Share');

		});

		//Update client warrrants
		$("#share_warrant_price").change(function() {
			var share = "fields[warrant_price]=" + $(this).asNumber();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#warrants_info", 'Share');
		//	formatMoney();
		});


		//Update client margin on taxes - load
		$("#share_margin_tax").change(function() {
			var share = "fields[margin_tax]=" + $(this).val();
			sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#margin_tax_info", 'Margin');

		});

		//Update addtional shares

		$("#share_addshares_amount").change(function() {	updateAddshares(true); });
		$("#share_addshares_price").change(function() {		updateAddshares(true);	});
		$("#share_addshares_clients").change(function() {	updateAddshares(true);	});
		updateAddshares(false);




		$("#doc_tabs").tabs();
		$("#doc_tabs").bind( "tabsshow", function(event, ui) { window.location.hash = ui.panel.id;});


		$('.doc_link').each(function(){


			$(this).click(function() {
/*
				var $preparingFileModal = $("#preparing-file-modal");
                $preparingFileModal.dialog({ modal: true }).delay(1500);
                return false;

				var href = $(this).attr('href');

			    $.fileDownload(href, {

		        	preparingMessageHtml: "We are preparing your report, please wait...",
					failMessageHtml: "There was a problem generating your report, please try again.",
					successCallback: function (url) {

                        $preparingFileModal.dialog('close');
                    },
                    failCallback: function (responseHtml, url) {

                        $preparingFileModal.dialog('close');
                        $("#error-modal").dialog({ modal: true });
                    }
			    });

				return false; //this is critical to stop the click event which will trigger a normal file download!
*/
			});
		});

	<?php endif; ?>


	<?php if( in_array($action, array('add','edit')) ): ?>

		<?php if($interface == 'users'): ?>

			$("#form-user").validate({
	            rules: {
	                'fields[username]': {required:true, minlength:2},
	                'fields[userpass]': {required:false, minlength:5}
	            }
	        });

		<?php endif; ?>

	<?php endif; ?>


	<?php if($action == 'profile'):?>


		loadNotes();
		var loadNotes_interval = setInterval("loadNotes()", 1500);


		//Stop updating notes panel if mouseover.
		$("#notes-panel").mouseenter(function(){

			console.log('clear....');
			clearInterval(loadNotes_interval);

		}).mouseleave(function(){

			loadNotes_interval = setInterval("loadNotes()", 1500);

		});


		<? $can_edit_notes = in_array($group, array(1,3)); ?>
		<? if($can_edit_notes): ?>
		setInterval("checkNoteChanges()", 1500);
		<? endif; ?>

		$('.resizable').each(function(){ $(this).resizable(); });

		$("#profile_tabs").tabs();

		//add notes to client
		$("#note-save").click(function() {

			var note = "note="+encodeURIComponent($('#note-val').val());
		 	sendData("<?=$base?>ajax/save_client_note/<?=$client->id?>", note, "#msg_updating_client", 'Notes');

		 	$('#note-val').val('');
		});

			//quick note buttons
			$("#quick-note-na").click(function(){quick_note("N/A.");});
			$("#quick-note-ct").click(function(){quick_note("Call Tomorrow.");});


		//update original notes
		$("#original-notes-save").click(function() {

			var value = CKEDITOR.instances['original_notes'].getData();

			var post = "fields[notes]="+encodeURIComponent(value);
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", post, "#msg_updating_client", 'Notes');

			CKEDITOR.instances['original_notes'].resetDirty();
		});

		//Update client address onchange
		$("#client_title").change(function() {
			var title = "fields[title]=" + $(this).val();
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", title, "#msg_updating_client", 'Title');
		});

		//Update client address onchange
		$("#client_name").change(function() {
			var name = "fields[name]=" + encodeURIComponent($(this).val());
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", name, "#msg_updating_client", 'Name');
		});

		//Update client address onchange
		$("#client_address").change(function() {
			var address = "fields[address]=" + encodeURIComponent($(this).val());
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", address, "#msg_updating_client", 'Address');
		});

		//Update client status onchange
		$("#client_status").change(function() {
			var status = "fields[id_status]="+$(this).val();
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", status, "#msg_updating_client", 'Status');
		});

		//Update client reference onchange
		$("#client_reference").change(function() {

			var id_ref = $(this).val();

			var ref = "fields[id_reference]="+id_ref;
		 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", ref, "#msg_updating_client", 'Reference');

		 	$('#ref_more').attr({ rel: '<?=$base?>ajax/ref_info/'+id_ref+'/'});

	 		var clone = $('#ref_more').clone();
	 		$('#ref_more').remove();

	 		$('#td_reference').append(clone);
	 		$('#ref_more').cluetip(clueTip_Options)

		 	if(id_ref == 0) $('#ref_more').fadeOut(500); else $('#ref_more').show();

			//ping client profile to update session info :p
			//$.ajax("<?=$base?>client/profile/<?=$client->id?>");
			window.location.reload();

		});




		//Update docs sent
		$(".check-docs-sent").each(function(){

			$(this).change(function(){

				var field = $(this).attr('id');
				var checked = ( $(this).attr('checked') ) ? '1' : '0';

				var data = "fields["+ field +"]="+checked;

			 	sendData("<?=$base?>ajax/update_client/<?=$client->id?>", data, "#msg_updating_client", 'Docs Sent');

			});

		});


		var clueTip_Options = {
 			cluetipClass: 'rounded', activation: 'click', ajaxCache: false, width: '426px',
 			arrows: true, sticky: true, showTitle: false, closePosition: 'top',
			closeText: '<button class="btn" style="position:absolute; right:8px; top:11px">X</button>',
		}

		//shares description and spa dialog
		$('.share-description').cluetip(clueTip_Options);


		//ref info
		$('#ref_more').cluetip(clueTip_Options);

	<? endif; ?>


	<? if( $interface == 'tools'):?>

		<? if( $action == 'send-mail' || $action == 'send-fax'):?>

			toggleMailFrom('company');

			$("input[name=mail[company-type]]:radio").change(function() {

				toggleMailFrom($(this).val());

				<? if( $action == 'send-mail'):?>

					$('#form-email').attr('action',$('#mailer-action-'+$(this).val()).val());

				<? endif; ?>

			});

			var curr_att = 1;
			$('#attach-more').click(function(){

				var original = $('#attachment-'+curr_att);
				var cloned = original.clone();

				curr_att++;

				cloned.attr('id', 'attachment-'+curr_att);

				cloned.insertAfter(original);

			});

		<? endif; ?>



		<? if($action == 'remove-client'):?>

			$('#remove_client_search').click(function(){

				var id_client = $('#remove_client_id').val();
				if(id_client>0)
				{
					var data = { 'id_client': id_client };

					$('#remove_client_info').html('Searching for client...');

					$.ajax({
						type: "POST", url: '<?=$base?>ajax/get_client/', data: data, cache: false,
						success: function(message){
							$('#remove_client_info').html(message);

							if(message != "No client found for this id.")
								$('#remove_client_btn').show();
							else
								$('#remove_client_btn').hide();

						}
					});
				}
			});
			$('#remove_client_btn').click(function(){

				var id_client = $('#remove_client_id').val();
				if(id_client>0)
				{
					if(confirm('Are you sure?'))
					{
						var data = { 'id_client': id_client };
						$('#remove_client_info').html('Removing client...');

						$.ajax({
							type: "POST", url: '<?=$base?>ajax/tool-delete_client/', data: data, cache: false,
							success: function(message){
								$('#remove_client_info').html(message);
								$('#remove_client_btn').hide();
							}
						});
					}
				}
			});

		<? endif; ?>


	<? endif; ?>




	// Side Navigation Menu Slide
	$("#nav > li > a.collapsed + ul").slideToggle(0);

	$("#nav > li > a").click(function() {
		$(this).toggleClass('expanded').toggleClass('collapsed').parent().find('> ul').slideToggle('medium');
	});

	$('input[placeholder], textarea[placeholder]').placeholder();


	formatMoney();

});



<?php if($action == 'profile'):?>

	$(window).bind('beforeunload', function(e) { return checkChanges(); });

<?php endif; ?>


<?php if($action == 'message_form'):?>


	var curr_att = 1;
	$('#attach-more').click(function(){
		var original = $('#attachment-'+curr_att);
		var cloned = original.clone();
		curr_att++;
		cloned.attr('id', 'attachment-'+curr_att);
		cloned.insertAfter(original);
	});

<?php endif; ?>





//No right-click
//$(document).bind("contextmenu", function(e){ preventRightClick(e); });

function preventRightClick(e){
	e.preventDefault();
	alert("Right-click is not allowed");
}


function loadNotes()
{

	console.log('updating_notes....');
	var url = '<?=$base?>ajax/get_notes/<?=$client->id?>';

	$.ajax(
	{
		type: "POST", url: url, data: '', cache: false,
		success: function(new_notes)
		{

			$("#notes-panel").html(new_notes);
		}
	});
}

function deleteNote(note_id)
{
	if(confirm("Are you sure you want to delete this note?"))
	{
		var url = '<?=$base?>ajax/delete_note/'+note_id;
		sendData(url, '', "#msg_updating_note", 'Note');
	}
}

//select stock
function selectClientShare(id)
{
	var share = "share_selected=true&id_client=<?=$client->id?>";
	sendData("<?=$base?>ajax/update_share/"+id, share, "#msg_updating_share", 'Share Selection');
}


//calc total_value onchange - manage shares
function calcClientShare()
{
	console.log('calc');
	var qty = $('#share_qty').val();
	var price = $('#share_price').asNumber();
	var total_value = qty * price;

	$('#share_total_value').val(total_value);
	formatMoney();
}

//Update stock info onchange - profile
function updateClientShare(id)
{
	var is_selected = $('#share_selected_'+id).val();

	console.log(is_selected);
	var qty = $('#share_qty_'+id).val();
	var price = $('#share_price_'+id).asNumber();
	var total_value = qty * price;

	$('#share_total_value_'+id).text(total_value);

	formatMoney();

	var share = "fields[shares]="+qty+"&fields[price]="+price+"&fields[total_value]="+total_value;
	sendData("<?=$base?>ajax/update_share/"+id, share, "#msg_updating_share", 'Share');
}

function updateClientShareDialog(id)
{
	var qty = $('#dialog-share_qty_'+id).val();
	var price = parseFloat($('#dialog-share_price_'+id).asNumber());

	$('#share_qty_'+id).val(qty);
	$('#share_price_'+id).val(price);

	updateClientShare(id);
	updateClientSPA(id);
}

function updateClientSPA(id)
{
	var recert_amount = $('#dialog-recert_amount_'+id).asNumber();
	var qty = $('#dialog-share_qty_'+id).val();
	var price = $('#dialog-share_price_'+id).asNumber();

	var gross_sales = qty * price;
	var recert_due = qty * recert_amount;
	var deductible_fee = gross_sales * 0.015;

	var net_sales = ( gross_sales + recert_due ) - deductible_fee;

	$('.recert_due').each(function(){ $(this).text(recert_due); });
	$('.gross_sales').each(function(){ $(this).text(gross_sales); });
	$('.deductible_fee').each(function(){ $(this).text(deductible_fee); });
	$('.net_sales').each(function(){ $(this).text(net_sales); });

	formatMoney();

	var share = "fields[recert_amount]="+recert_amount;
	sendData("<?=$base?>ajax/update_share/"+id, share, "#msg_updating_share", 'Recert Amount');
}


function updateAddshares(onchange)
{

	var amount = $("#share_addshares_amount").val();
	var price = $("#share_addshares_price").asNumber();
	var clients = $("#share_addshares_clients").val();

	if(amount>0 && clients>0) var total = amount*clients; else var total = 0;
	if(amount>0 && price>0) var due = amount * price; else var due = 0;

	$("#share_addshares_amount").val(amount);
	$("#share_addshares_price").val(price);
	$("#share_addshares_due").html(due);
	$("#share_addshares_clients").val(clients);
	$("#share_addshares_total").html(total);

	if(onchange && due>0)
	{
		var share = "fields[addshares_amount]="+amount+"&fields[addshares_clients]="+clients+"&fields[addshares_price]="+price;
		sendData("<?=$base?>ajax/update_share/<?=isset($share) && is_object($share) ? isset($share) && is_object($share) ? $share->id : '' : ''?>", share, "#addshares_info", 'Share');
	}
	formatMoney();


/**/


}



function toggleMailFrom(which)
{
	$('#company-from').hide();
	$('#registry-from').hide();
	$("input", $("#company-from")).attr("disabled", true);
	$("input", $("#registry-from")).attr("disabled", true);

	$("input", $("#"+which+"-from")).attr("disabled", false);
	$('#'+which+'-from').effect("pulsate", { times:1}, 3000);

	console.log('MailFrom set: ' + which);
}

function sendData(url, data, div, what)
{
	$(div).text('Updating '+ what +'...');

	$.ajax(
	{
		type: "POST", url: url, data: data, cache: false,
		success: function(message)
		{

			$(div).text(what+ ' updated succesfully!');
			setTimeout(function(){ $(div).text('');},4000)
		}
	});
}

function checkChanges()
{
	var check_sml_note = $('#note-val').val().length;
	var check_ori_note = null;

	<?php $can_edit_notes = in_array($group, array(1,3)); ?>
	<?php if($can_edit_notes): ?>
	var check_ori_note = CKEDITOR.instances.original_notes.checkDirty();
	<?php endif; ?>



	if ( check_sml_note || check_ori_note )
        return "You have unsaved notes.";
}

function checkNoteChanges()
{
	if( CKEDITOR.instances['original_notes'].checkDirty() )
		$('#original-notes-save').fadeIn(2000);
	else
		$('#original-notes-save').fadeOut(500);
}

function quick_note(str_note)
{
	var note = "note="+encodeURIComponent(str_note);
	sendData("<?=$base?>ajax/save_client_note/<?=$client->id?>", note, "#msg_updating_client", 'Notes');
}

var currency_opts = { negativeFormat: '-%s%n', positiveFormat: '%s%n', roundToDecimalPlace: 2 }
function formatMoney(){

	// Format all money inputs
	$('.money').each(function(){
		$(this).formatCurrency(currency_opts).text();
	});
}

function sendDoc(which)
{
	var agent_email = '<?=$curr_client->company_agent_email?>';
	if(!agent_email.length){ alert('No agent assigned for this client.'); return false;	}

	var ref_id = '<?=$curr_client->id_reference?>';
	if(which == 'NDA' && ref_id=='0'){ alert('No reference assigned for this client.');	return false; }

	var send_to = $('input[name=client_emails]:checked').val();
	if (typeof send_to !== "undefined") {

		if(confirm('Are you sure you want to send the '+which+"?\nFrom: "+agent_email+"\nTo: "+send_to)){

			$('#msg_status_docs').show();
			$('#msg_status_docs').html('Generating '+ which +'...');
			$('#btn_send_'+which).val('Sending '+which+'...');

			var docs = [];
			if(which.indexOf("+") != -1){
				docs = which.split('+');
			}
			else
				docs.push(which);

			getAttachs(docs).done(function(attachs) {
				$('#msg_status_docs').html(which +' Generated. Sending E-mail... (it may take a while, please wait)');
				var url = '<?=$base?>ajax/sendDocument/'+send_to+'/'+which;
				$.ajax({
					url:url,
					type: "post",
					data: { 'attachs' : attachs },
					success: function(data) {

						$('#msg_status_docs').html('Finished. '+ which + ' was sent.');
						$('#btn_send_'+which).val(which + ' was sent.');

						var note = "note="+encodeURIComponent(which+' Sent.');
						sendData("<?=$base?>ajax/save_client_note/<?=$curr_client->id?>", note, "#msg_updating_client", 'Notes');

						if(which == 'SPA')
						{
							$("#client_status").val(4);
							$("#client_status").change();
						}
					},
					error: function() { $('#btn_send_'+which).val('Error sending '+which+'. Please reload page.'); }
				});
			});
		}
	}
	else
		alert('Please select one of the client\'s e-mails on the box below.');
}

function getAttachs(docs){
	var count = docs.length;
	var attachs = [];
	var all_done = $.Deferred();
	$.each(docs, function (index, doc) {
		var url = '<?=$base?>client/documents/'+doc+'/save';
		var deferred = $.get(url)
		.done(function(data){ attachs.push(data); })
		.complete(function() {
			count--;
			if(count === 0)
				all_done.resolve(attachs);
		});
	})
	return all_done.promise();
}

function sendMessage(which, action, label){

	if(action == 'send'){
		var send_to = $('input[name=client_emails]:checked').val();
		if (typeof send_to === "undefined"){
			alert('Please select one of the client\'s e-mails on the box below.');
			return;
		}
                $('#msg_status').html("Sending Message...");
	}

	if(action == 'preview')
		$('#msg_status').hide();

	$('#msg_preview').show();
	$('#msg_subject').html("loading...");

	var url = '<?=$base?>ajax/sendMessage/'+which+'/'+action+'/'+send_to;
	$.ajax(url, {
		success: function(data) {

			$('#msg_from_type').html(data.from_type);
			$('#msg_subject').html(data.subject);
			$('#msg_body').html(data.body);

			if(action == 'send'){
				$('#msg_status').html(data.send_result);

				var note = "note="+encodeURIComponent('Message Sent: '+data.subject);
				sendData("<?=$base?>ajax/save_client_note/<?=$curr_client->id?>", note, "#msg_updating_client", 'Notes');


			}
		},
		error: function() { $('#msg_status').html("Problem fetching or sending message. Contact support."); }
	});
}



function loadMessage(which){
	if(which == 'custom'){
		$('#msg_subject').val("");
		setCK("");
	}

	$('#msg_subject').val("loading...");
	var url = '<?=$base?>ajax/loadMessage/'+which+'/preview/';
	$.ajax(url, {
		success: function(data) {
			//$('#msg_from_type').html(data.from_type);
			$('#msg_subject').val(data.subject);

			var d_body = b64DecodeUnicode(data.body);
			console.log(d_body);
			setCK(d_body);
		},
		error: function() { setCK("Problem fetching or sending message. Contact support."); }
	});
}
function checkSendMessage(){

	var send_to = $('input[name=client_emails]:checked').val();

	if (typeof send_to === "undefined"){
		alert('Please select one of the client\'s e-mails on the box below to continue.');
		return false;
	}

	if(confirm('Are you sure?')){

		$('#msg_sendbtn').hide();
		$('#msg_status').html('Sending...');

		CKEDITOR.instances.msg_body.updateElement();

		var action = $('#form_msg').attr('action');
		var form = $('#form_msg')[0];
	    var data = new FormData(form);

  		$.ajax({
            type: "POST",
            enctype: 'multipart/form-data',
            url: (action + send_to),
            data: data,
            processData: false,
            contentType: false,
            cache: false,
            timeout: 600000,
            success: function (data) {
				console.log("Result: ", data);

				$('#msg_status').html(data.send_result);
				$('#msg_sendbtn').show();

				if(!data.error){
					var note = "note="+encodeURIComponent('Message Sent: '+data.subject);
					sendData("<?=$base?>ajax/save_client_note/<?=$curr_client->id?>", note, "#msg_updating_client", 'Notes');
				}
            },
            error: function (e) {
                console.log("ERROR: ", e);
				$('#msg_status').html(e.responseText);
		//		$('#msg_sendbtn').show();
            }
        });
	}
}

function setCK(text){ CKEDITOR.instances.msg_body.setData(text, function(){ this.checkDirty(); });}

function b64DecodeUnicode(str) {
    // Going backwards: from bytestream, to percent-encoding, to original string.
    return decodeURIComponent(atob(str).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
}

</script>


<?php if($view == 'client/messages'): ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jqc-1.12.4/dt-1.10.18/datatables.min.css"/>
<script type="text/javascript" src="https://cdn.datatables.net/v/dt/jqc-1.12.4/dt-1.10.18/datatables.min.js"></script>

<script>
	$(document).ready( function () {
		$('#tbl_messages').DataTable({
			"pageLength": 30,
			"lengthChange": false,
			 "order": [[ 0, "desc" ]]
		});
		setTimeout(function(){
			window.location.reload(1);
		}, 300000);
	} );
</script>
<?php endif; ?>

<?php if($view == 'client/messages_sent'): ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/jqc-1.12.4/dt-1.10.18/datatables.min.css"/>
<script type="text/javascript" src="https://cdn.datatables.net/v/dt/jqc-1.12.4/dt-1.10.18/datatables.min.js"></script>
<script>
	$(document).ready( function () {

		setTimeout(function(){
			window.location.reload(1);
		}, 300000);

		var table = $('#tbl_messages').DataTable({
			"pageLength": 30,
			"lengthChange": false,
			 "order": [[ 0, "desc" ]]
		});

		$('#tbl_messages').on('click', 'tr.details-control', function () {
			var tr = $(this).closest('tr');
			var row = table.row( tr );

			if ( row.child.isShown() ) {
				row.child.hide();
			//	tr.removeClass('shown');
			}
			else {
				var msg_id = tr.data('msgid');
				$.get("<?=$base?>ajax/getSent/"+msg_id).done(function(data){
					row.child(data).show();
			//		tr.addClass('shown');
				})
			}
		} );
	} );
</script>
<?php endif; ?>

