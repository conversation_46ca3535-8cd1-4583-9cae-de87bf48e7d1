<?php

	$group = $current_user['userinfo']->id_group;

	//1 - seller
	//2 - admin
	//3 - load
	//4 - registry
	//5 - tech admin
	//6 - compliance


	//Client Options
	$arr['header'] 	= array('Client' => 'client');
	$arr['options'] = array('My Leads' => 'search','Messages' => 'messages');

	if($curr_client){

		$arr['options'] = array_merge(
								$arr['options'],
								array(
									'Profile and Notes' => 'profile',
									'Documents' 		=> 'documents',
									'Audit' 			=> 'audit'
								)
		);
	}

	$nav[] = $arr;


	//limit navigation except for admin and tech admin group

	if ( in_array($group, array(1,3) ))
	{
		$arr['header'] 	= array('Messages' => 'client');
		$arr['options'] = array('Inbox' => 'messages','Sent' => 'messages_sent');

		$nav[] = $arr;


		$arr['header'] 	= array('Tools' => 'tools');
		$arr['options'] = array(
									'Change/Manage All Leads' 	=> 'change',
									//'Send E-mail' 	=> 'send-mail',
									'Send Fax' 		=> 'send-fax',
									'Import Leads from List' => 'import',
									'Manage Duplicates' => 'duplicates',
									'Remove Lead'	=> 'remove-client'
								);
		$nav[] = $arr;



		//Companies
		$arr['header'] 	= array('Companies' => 'companies');
		$arr['options'] = array(
									'Directory' 			   => 'manage',
									'Add new Company/Registry' => 'add'
								);
		$nav[] = $arr;


		//Reports
		$arr['header'] 	= array('Reports' => 'reports');
		$arr['options'] = array(
									'Lead Status by List' => 'leads-status-by-list',
								);
		$nav[] = $arr;
	//

	}
	else
	{

		//Companies
		$arr['header'] 	= array('Companies' => 'companies');
		$arr['options'] = array('Directory' => 'manage');
		$nav[] = $arr;

	}

	if ( in_array($group, array(3) ))
	{

	//Admin
		$arr['header'] 	= array('Users' => 'users');
		$arr['options'] = array(
									'Manage' 	  => 'manage',
									'Add new User' => 'add'
								);
		$nav[] = $arr;

		$arr['header'] 	= array('Maintenance' => 'maintenance');
		$arr['options'] = array(
									'Lists'			=> 'lists',
									'References'	=> 'refs',
									'Positions'		=> 'positions',
									'Statuses'		=> 'statuses',
									'Phone Types'	=> 'phone_types',
									'Trunks'		=> 'trunks',
									'Add Lead'		=> 'clients'
								);
		$nav[] = $arr;

	}

	// show add lead for admin
	if ( in_array($group, array(1) ))
	{


		$arr['header'] 	= array('Maintenance' => 'maintenance');
		$arr['options'] = array(
									'Lists'			=> 'lists',
									'Add Lead'		=> 'clients'
								);
		$nav[] = $arr;

	}






?>
