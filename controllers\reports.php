<?
	$action = $var1;
	$id = intval($var2);
	$action = (empty($action))?'manage':$action;


	$subaction = $var3;
	$id_employee = $var4;

	$sent = $_REQUEST['sent'];
	$sent_employee = $_REQUEST['sent_employee'];

	//die($action);

	switch($action)
	{
		case 'leads-status-by-list':


			$view = $interface.'/'.$action;

			$filterSent = $_REQUEST['filterSent'];
			$filters = $_REQUEST['filters'];

			if($filterSent)
			{

				foreach($_REQUEST['filters'] as $filter_name => $filter):

					if(!empty($filter) || $filter==='0'):

						switch($filter_name):

							case 'name':
							{
								$filter_clause .= "AND name LIKE '%" . $db->real_escape_string($filter) . "%' ";
								break;
							}

							default: $filter_clause = NULL; break;

						endswitch;

					endif;

				endforeach;
			}
			//else $filter_clause .= "AND is_active = '1' ";


			$query = "SELECT * FROM lists WHERE id<>0 $filter_clause ORDER BY name";
			$rs_lists = $db->query($query);

			while ( $list = $db->fetch_object($rs_lists))
			{
/*
	Unassigned	Available	Wrong Number	SPA Sent	Banking Sent	TOL	Heat	SPA cancelled	contacted

Unassigned
Available
Contacted
SPA Sent
Banking Sent
TT's Recvd
Landed
Cleared
TOL
CXL
Wrong Number
Heat
*/

				$status_where = "WHERE id IN (0,1,13,11,4,7,9,2,10)";
				$query = "SELECT id,name FROM statuses $status_where";
				$rs = $db->query($query);
				while ( $s = $db->fetch_object($rs))
				{
					$query = "SELECT count(id) as total FROM clients WHERE id_list = " . intval($list->id) . " AND id_status = " . intval($s->id);
					$rl = $db->query($query);
					$l = $db->fetch_object($rl);

					$report[$list->name][$s->name] = $l->total;


				}

				//TT's Recvd
                                $query = "SELECT count(id) as total FROM clients WHERE id_list = " . intval($list->id) . " AND notes LIKE '% TT %'";
                                $rs = $db->query($query);
                                $r = $db->fetch_object($rs);

                                $report[$list->name]['TT\'s Recvd'] = $r->total;

				//Landed
                                $query = "SELECT count(id) as total FROM clients WHERE id_list = " . intval($list->id) . " AND notes LIKE '%LANDED%'";
                                $rs = $db->query($query);
                                $r = $db->fetch_object($rs);

                                $report[$list->name]['Landed'] = $r->total;

				//Cleared
                                $query = "SELECT count(id) as total FROM clients WHERE id_list = " . intval($list->id) . " AND notes LIKE '%CLEARED%'";
                                $rs = $db->query($query);
                                $r = $db->fetch_object($rs);

                                $report[$list->name]['Cleared'] = $r->total;


			}



                        $report_status = $db->getQueryList("SELECT name FROM statuses $status_where");

                        // Append custom status labels
                        $report_status[] = (object)array('name' => "TT\'s Recvd");
                        $report_status[] = (object)array('name' => 'Landed');
                        $report_status[] = (object)array('name' => 'Cleared');
//			printr($report_status);
//			die();




		break;


		//list
		default:

		break;
	}

	//include("controllers/client/$action.php");




?>