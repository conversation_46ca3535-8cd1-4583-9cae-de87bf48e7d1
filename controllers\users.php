<?php
	$action = $var1;
	$id = intval($var2);
	$action = (empty($action))?'manage':$action;
	$sent = $_REQUEST['sent'];

	switch($action)
	{
		case 'add':
		case 'edit':

			$view = 'users/form';


			if($sent)
			{

				require_once('_lib/pkbdf2.php');
				$salt = uniqid();

				$fields = $_REQUEST['fields'];

				if($action == 'add')
				{
					$fields['salt'] = $salt;

					$userpass = ( strlen($fields['userpass']) ) ? $fields['userpass'] : 'changeme';
					$hashpass = base64_encode(pbkdf2('sha256', $userpass, $salt, 12000, 32, true));

					$fields['userpass'] = $hashpass;

					$fields['created_at'] = date('Y-m-d H:i:s');

					$query = $db->prepare_insert($fields, 'users');
					if($db->query($query))
						header("location: {$base}users");

				}

				elseif ( $action == 'edit')
				{
					if( strlen($fields['userpass']) )
					{
						$userpass = $fields['userpass'];
						$hashpass = base64_encode(pbkdf2('sha256', $userpass, $salt, 12000, 32, true));

						$fields['userpass'] = $hashpass;
						$fields['salt'] = $salt;
					}
					else
						unset($fields['userpass']);


					$fields['updated_at'] = date('Y-m-d H:i:s');



					$query = $db->prepare_update($fields, 'users', "WHERE id = $id");

					if($db->query($query))
						$msg = "User updated successfully";

				}

			}



			if($id)
				$user = $db->fetch_object($db->query("SELECT * FROM users WHERE id = $id"));


			$so['groups'] = $db->select_values('id', 'name', 'groups', $user->id_group, 'WHERE is_active = 1');	//get active groups

		break;


		//list
		default:

			$view = 'users/manage';

			$filterSent = $_REQUEST['filterSent'];
			$filters = $_REQUEST['filters'];

			if($filterSent)
			{

				foreach($_REQUEST['filters'] as $filter_name => $filter):

					if(!empty($filter) || $filter==='0'):

						switch($filter_name):

							case 'username':
							{
								$filter_clause .= "AND u.username LIKE '%$filter%' ";
								break;
							}

							case 'is_active':
							{
								$filter_clause .= "AND u.is_active = '{$filter}' ";
								break;
							}

							default: $filter_clause = NULL; break;

						endswitch;

					endif;

				endforeach;
			}
			else{ $filter_clause = "AND u.is_active = '1'"; }


			$query = "SELECT u.*, g.name as usergroup FROM users u, groups g
						WHERE u.id_group = g.id AND u.id NOT IN (0,1) $filter_clause ORDER BY u.username, u.id_group";

			$rs_users = $db->query($query);

			while ( $user = $db->fetch_object($rs_users)) $users[] = $user;

			$total_users = isset($users) && is_array($users) ? count($users) : 0;

		break;
	}

	//include("controllers/client/$action.php");




?>