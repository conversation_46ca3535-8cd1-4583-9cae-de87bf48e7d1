version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "8080:80"
    environment:
      DB_HOST: db
      DB_NAME: crm2
      DB_USER: crmuser
      DB_PASS: crmpass
    depends_on:
      - db
  db:
    image: mysql:5.7
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: example
      MYSQL_DATABASE: crm2
      MYSQL_USER: crmuser
      MYSQL_PASSWORD: crmpass
    volumes:
      - db_data:/var/lib/mysql

volumes:
  db_data:
