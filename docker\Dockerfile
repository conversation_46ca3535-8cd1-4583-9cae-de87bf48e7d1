FROM php:7.4-fpm

# Install system packages and PHP extensions required by the CRM
RUN apt-get update && apt-get install -y \
    nginx \
    wkhtmltopdf \
    libpng-dev libjpeg-dev libfreetype6-dev \
    libonig-dev libxml2-dev libcurl4-openssl-dev \
    libzip-dev libicu-dev \
    git unzip curl gnupg \
 && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
 && apt-get install -y nodejs \
 && docker-php-ext-install mysqli mbstring gd \
 && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Composer globally
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copy application code
COPY . /var/www/html
WORKDIR /var/www/html

# Install PHP dependencies if needed
# This section previously installed phpMyAdmin dependencies, which have been removed

# Install JS dependencies for the cluetip plugin
R<PERSON> if [ -f _lib/js/cluetip/package.json ]; then \
        cd _lib/js/cluetip && npm install && cd ../../.. ; \
    fi

# Copy nginx configuration and startup script
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY docker/start.sh /start.sh
RUN chmod +x /start.sh

# Expose HTTP port
EXPOSE 80

# Start PHP-FPM and nginx
CMD ["/start.sh"]
