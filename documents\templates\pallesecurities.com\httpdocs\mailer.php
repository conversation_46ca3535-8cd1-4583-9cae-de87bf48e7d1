<?php
error_reporting(E_ALL);
$to      = '';
$from    = '';
$subject = '';
$message = '';
$domain  = '';
if(isset($_SERVER['HTTP_REFERER'])) $domain = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);

if(isset($_POST['channel'])){ // && left($_SERVER['REMOTE_ADDR'],12)==left($_POST['channel'],12)){
	if($_SERVER['REQUEST_METHOD'] == 'POST'){ //

		// email fields: to, from, subject, and so on
		$to 	 = filter_var(trim($_POST["to"]), FILTER_SANITIZE_EMAIL);
		$from 	 = filter_var(trim($_POST["from"]), FILTER_SANITIZE_EMAIL);
		$subject = htmlspecialchars(trim($_POST["subject"]), ENT_QUOTES, 'UTF-8');
		$message = htmlspecialchars(trim($_POST["message"]), ENT_QUOTES, 'UTF-8');
		$group   = intval($_POST["group"]);

		// Validate email addresses
		if (!filter_var($to, FILTER_VALIDATE_EMAIL) || !filter_var($from, FILTER_VALIDATE_EMAIL)) {
			echo "<p>Invalid email address provided!</p>";
			echo "<p><a href=\"javascript:;\" onClick=\"history.back();\">Click here to go back.</a></p>";
			exit;
		}

		// send
		$ok = mime_mail();
		if ($ok) {
			echo "<p>mail successfully sent to " . htmlspecialchars($to) . "!</p>";
			echo "<a href=\"javascript:;\" onClick=\"location.replace('" . htmlspecialchars($_SERVER['HTTP_REFERER']) . "');\">Click here to go back.</a></p>";
		} else {
			echo "<p>mail could not be sent!</p>";
			echo "<p><a href=\"javascript:;\" onClick=\"location.reload(true);\">Click here to resend again.</a> or ";
			echo "<a href=\"javascript:;\" onClick=\"history.back();\">Click here to go back.</a></p>";
		}
		exit;
	}else{
		echo "<h1>Invalid Request: Not allowed to be invoked outside scope.</h1>";
	}
}

function post_mailer(){
global $to,$from,$subject,$message,$domain;

	if($_SERVER['REQUEST_METHOD'] == 'POST'){
		$domain  = str_replace('www.','',$domain);
		$to      = "client@$domain";
		$from    = "webmaster@$domain";
		$subject = "New Mailing List - $domain";
		$message = "Mailing List Form Data:\n\n";
		// each() was deprecated in PHP 7.2.0 and removed in PHP 8.0.0
		foreach($_POST as $key => $val){
			if(strtolower($key)!='submit')
				$message .= str_pad(str_replace("_"," ",$key),15," ",STR_PAD_LEFT) . " = " . str_replace("\n","\n\t\t",htmlspecialchars($val)) . "\n";
		}
		return mime_mail();
	}else{
		return false;
	}
}

function mime_mail(){
global $to,$from,$subject,$message;

	// boundary
	$mime_boundary = '==x_x_' . md5(uniqid(time())) . '_x_x==';

	// headers for attachment
	// multipart boundary
    $header = "From: $from\n";
    $header .= "Return-Path: $from\n";
    $header .= "Return-Receipt-To: $from\n";
    $header .= "Reply-To: $from\n";
	if(isset($_POST['group'])){
		$header .= ($_POST['group'] == '0' ? "Bcc: <EMAIL>\n" : "Bcc: <EMAIL>\n");
	}
	$header .= "Date: " . date("r") . "\n";
    $header .= "MIME-Version: 1.0\n";
	$header .= "X-Priority: 3\n";
	$header .= "X-Mailer: CRM Mailer 1.0\n";

	// preparing attachments
	if(isset($_FILES['upfile'])){
		$header .= "Content-Type: multipart/mixed;\n";
		$header .= "	 boundary=\"$mime_boundary\"\n";
		$header .= "\nThis is a multi-part message in MIME format.\n\n";
		$header .= "--$mime_boundary\n";
		$header .= "Content-type:text/plain;\n";
		$header .= "	 charset=\"iso-8859-1\"\n";
		$header .= "Content-Transfer-Encoding: 7bit\n\n";
		$header .= "$message\n";
		foreach($_FILES['upfile']["error"] as $key => $error){
			if($error == UPLOAD_ERR_OK){
				$data = chunk_split(base64_encode(fread(fopen($_FILES['upfile']['tmp_name'][$key], "rb"), filesize($_FILES['upfile']['tmp_name'][$key]))));
				$header .= "--$mime_boundary\n";
				$header .= "Content-Type: " . $_FILES['upfile']['type'][$key] . ";\n";
				$header .= "	 name=\"" . $_FILES['upfile']['name'][$key] . "\"\n";
				$header .= "Content-Transfer-Encoding: base64\n";
				$header .= "Content-Disposition: attachment;\n";
				$header .= "	 filename=\"" . $_FILES['upfile']['name'][$key] . "\"\n\n";
				$header .= "$data\n";
			} else {
				echo "<p>mail could not be sent! Problem with attachments.</p>";
				echo "<p><a href=\"javascript:;\" onClick=\"location.reload(true);\">Click here to resend again.</a> or ";
				echo "<a href=\"javascript:;\" onClick=\"history.back();\">Click here to go back.</a></p>";
				exit;
			}
		}
		$header .= "--$mime_boundary--";
	}else{
		$header .= "Content-type:text/plain;\n";
		$header .= "	 charset=\"iso-8859-1\"\n";
		$header .= "Content-Transfer-Encoding: 7bit\n\n";
		$header .= "$message\n";
	}
	return @mail($to, $subject, '', $header, '-f' . $from);
}
?>
