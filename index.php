<?php
	// Start output buffering to prevent "headers already sent" issues
	ob_start();

	// Start the session at the very beginning before any output
	session_start();

	// Enable comprehensive error reporting and logging
	error_reporting(E_ALL);
	ini_set('display_errors', 1);
	ini_set('log_errors', 1);
	ini_set('error_log', __DIR__ . '/logs/php_errors.log');

	// Include enhanced error handler
	include('_lib/error_handler.php');
	include('_lib/config.inc.php');
	date_default_timezone_set('Africa/Johannesburg');

	//handle url routing
	$base = explode('/',$_SERVER['SCRIPT_NAME']);	array_pop($base);
	$uri  = explode('/',$_SERVER['REQUEST_URI']);	$uri = array_slice( $uri, count($base));

	// Initialize variables to prevent undefined offset errors
	$interface = isset($uri[0]) ? $uri[0] : '';
	$var1 = isset($uri[1]) ? $uri[1] : '';
	$var2 = isset($uri[2]) ? $uri[2] : '';
	$var3 = isset($uri[3]) ? $uri[3] : '';
	$var4 = isset($uri[4]) ? $uri[4] : '';
	$var5 = isset($uri[5]) ? $uri[5] : '';

	$base = implode('/',$base).'/';
	$command = implode('/',$uri);


	if($interface != 'api'){
		//check login
		include('controllers/login.php');
	}

	//route requests
	$interface = (empty($interface)) ? 'client' : $interface;

	switch($interface)
	{
		case 'ajax':
		case 'api':
		{
			$controller_only = true;
			$controller = "{$interface}/$var1";
			break;
		}

		case 'client':
		case 'tools':
		case 'companies':
		case 'users':
		case 'maintenance':
		case 'reports':
		{
			$controller = $interface;
			break;
		}

		case 'mail-ok':
		{
			$interface = $controller = 'client';
			$msg = 'Mail was sent successfully! Please, check your mailbox for confirmation.';

			break;
		}

		case 'logout':
		{
			session_unset();
			session_destroy();
			// Clear any buffered output
			ob_end_clean();
			header("location: $base");
			exit();
		}

		default:
		{
			// Clear any buffered output
			ob_end_clean();
			header("location: {$base}client/search");
			exit();
		}
	}

	//load client from session
	if($_SESSION['current_client']) $curr_client = $_SESSION['current_client'];

	//load interface controller
	if(strlen($controller) && is_file("controllers/{$controller}.php"))	include("controllers/{$controller}.php");
	else exit('invalid request.');

	//if it isn't an ajax request show html structure and views
	if(!isset($controller_only) || !$controller_only)	include('views/structure.php');


?>
