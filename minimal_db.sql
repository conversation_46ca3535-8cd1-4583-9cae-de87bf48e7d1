-- Minimal database schema for LionCRM testing
-- This file contains the essential tables needed for the CRM system to function properly

-- Set SQL mode to allow zero values in auto-increment fields
SET SESSION sql_mode='NO_AUTO_VALUE_ON_ZERO';

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `userpass` varchar(255) NOT NULL,
  `salt` varchar(50) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `id_group` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert a default admin user (username: tech, password: nospoon)
INSERT INTO `users` (`id`, `username`, `userpass`, `salt`, `email`, `id_group`, `is_active`, `created_at`) VALUES
(1, 'tech', 'YWJjMTIzNHNhbHQ=', 'salt123', '<EMAIL>', 3, 1, NOW());

-- Create countries table
CREATE TABLE IF NOT EXISTS `countries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `iso3166` varchar(2) DEFAULT NULL,
  `calling_code` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert a default country
INSERT INTO `countries` (`id`, `name`, `iso3166`, `calling_code`) VALUES
(404, 'Unknown', 'XX', '0');

-- Create companies table
CREATE TABLE IF NOT EXISTS `companies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `address` text,
  `city` varchar(100) DEFAULT NULL,
  `nationality` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `fax` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `id_registry` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create company_employees table
CREATE TABLE IF NOT EXISTS `company_employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `title` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `id_company` int(11) NOT NULL,
  `id_user` int(11) DEFAULT NULL,
  `id_position` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create statuses table
CREATE TABLE IF NOT EXISTS `statuses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default statuses
INSERT INTO `statuses` (`id`, `name`, `is_active`) VALUES
(0, 'Unassigned', 1),
(1, 'New', 1),
(2, 'In Progress', 1),
(3, 'Completed', 1);

-- Create lists table
CREATE TABLE IF NOT EXISTS `lists` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `origin` varchar(100) DEFAULT NULL,
  `comment` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` varchar(100) DEFAULT NULL,
  `total_new` varchar(100) DEFAULT '0',
  `total_duplicates` varchar(100) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create clients table
CREATE TABLE IF NOT EXISTS `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(20) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `address` text,
  `notes` text,
  `id_country` int(11) DEFAULT NULL,
  `id_status` int(11) DEFAULT '0',
  `id_company` int(11) DEFAULT '0',
  `id_registry` int(11) DEFAULT '0',
  `id_user_agent` int(11) DEFAULT '0',
  `id_employee` int(11) DEFAULT '0',
  `id_reference` int(11) DEFAULT '0',
  `id_user_registry` int(11) DEFAULT '0',
  `id_list` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_canceled` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create phone_types table
CREATE TABLE IF NOT EXISTS `phone_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default phone types
INSERT INTO `phone_types` (`id`, `name`) VALUES
(1, 'Mobile'),
(2, 'Fax'),
(3, 'Home'),
(4, 'Work');

-- Create client_contacts table
CREATE TABLE IF NOT EXISTS `client_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contact` varchar(100) NOT NULL,
  `contact_type` varchar(20) NOT NULL,
  `id_phone_type` int(11) DEFAULT '1',
  `id_client` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create client_shares table
CREATE TABLE IF NOT EXISTS `client_shares` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `shares` int(11) DEFAULT '0',
  `price` decimal(10,2) DEFAULT '0.00',
  `total_value` decimal(10,2) DEFAULT '0.00',
  `recert_amount` decimal(10,2) DEFAULT '0.00',
  `warrants` int(11) DEFAULT '0',
  `warrant_price` decimal(10,2) DEFAULT '0.00',
  `margin_open` decimal(10,2) DEFAULT '0.00',
  `margin_warrants` decimal(10,2) DEFAULT '0.00',
  `share_selected` tinyint(1) DEFAULT '0',
  `id_client` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create refs table
CREATE TABLE IF NOT EXISTS `refs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `fax` varchar(50) DEFAULT NULL,
  `address` text,
  `id_user` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create positions table
CREATE TABLE IF NOT EXISTS `positions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `company_type` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
