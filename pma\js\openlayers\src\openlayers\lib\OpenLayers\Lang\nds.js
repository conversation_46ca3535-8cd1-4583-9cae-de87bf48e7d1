/* Translators (2009 onwards):
 *  - Slomox
 */

/**
 * @requires OpenLayers/Lang.js
 */

/**
 * Namespace: OpenLayers.Lang["nds"]
 * Dictionary for Plattdüütsch.  Keys for entries are used in calls to
 *     <OpenLayers.Lang.translate>.  Entry bodies are normal strings or
 *     strings formatted for use with <OpenLayers.String.format> calls.
 */
OpenLayers.Lang["nds"] = OpenLayers.Util.applyDefaults({

    'unhandledRequest': "Unbehannelt Trüchmellels för de Anfraag ${statusText}",

    'permalink': "Permalink",

    'overlays': "Overlays",

    'baseLayer': "Achtergrundkoort",

    'sameProjection': "De Översichtskoort geiht blot, wenn de sülve Projekschoon as bi de Hööftkoort bruukt warrt",

    'readNotImplemented': "Lesen is nich inricht.",

    'writeNotImplemented': "<PERSON><PERSON><PERSON><PERSON> is nich inricht.",

    'noFID': "En Feature, dat keen FID hett, kann nich aktuell maakt warrn.",

    'errorLoadingGML': "Fehler bi’t Laden vun de GML-Datei ${url}",

    'browserNotSupported': "Dien Browser ünnerstütt keen Vektorbiller. Ünnerstütt Renderers:\n${renderers}",

    'componentShouldBe': "addFeatures : Kumponent schull man den Typ ${geomType} hebben",

    'getFeatureError': "getFeatureFromEvent is von en Laag ahn Render opropen worrn. Dat bedüüdt normalerwies, dat en Laag wegmaakt worrn is, aver nich de Handler, de dor op verwiest.",

    'commitSuccess': "WFS-Transakschoon: hett klappt ${response}",

    'commitFailed': "WFS-Transakschoon: hett nich klappt ${response}",

    'scale': "Skaal = 1 : ${scaleDenom}",

    'layerAlreadyAdded': "Du versöchst de Laag „${layerName}“ to de Koort totofögen, man de is al toföögt",

    'methodDeprecated': "Disse Methood is oold un schall dat in 3.0 nich mehr geven. Bruuk dor man beter ${newMethod} för.",

    'boundsAddError': "De Weert x un y, de mööt all beid an de add-Funkschoon övergeven warrn.",

    'lonlatAddError': "De Weert lon un lat, de mööt all beid an de add-Funkschoon övergeven warrn.",

    'pixelAddError': "De Weert x un y, de mööt all beid an de add-Funkschoon övergeven warrn.",

    'unsupportedGeometryType': "Nich ünnerstütt Geometrie-Typ: ${geomType}",

    'pagePositionFailed': "OpenLayers.Util.pagePosition güng nich: Element mit de Id ${elemId} is villicht an’n verkehrten Platz."

});
