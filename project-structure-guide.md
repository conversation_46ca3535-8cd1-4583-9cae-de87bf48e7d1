# Project Structure Analysis Guide

To better understand this codebase, follow these steps:

## 1. Generate Directory Structure

Run one of these commands in your terminal at the project root:

**Windows (PowerShell):**
```powershell
Get-ChildItem -Recurse | Where-Object { !$_.PSIsContainer } | Select-Object FullName | Out-File project-structure.txt
```

**Mac/Linux:**
```bash
find . -type f -not -path "*/node_modules/*" -not -path "*/\.*" | sort > project-structure.txt
```

## 2. Identify Key Files

Look for these important files:
- `package.json` - Lists dependencies and scripts
- Entry point files like `app.js`, `index.js`, or `server.js`
- Configuration files like `.env.example` or `config.js`
- Database connection files or models

## 3. Analyze Dependencies

Check package.json to understand:
- Framework (Express, React, Angular, etc.)
- Database (Mongoose, Sequelize, etc.)
- Testing frameworks (Jest, <PERSON>cha, etc.)

## 4. Review File Organization

Common patterns:
- MVC (Model-View-Controller)
- Component-based architecture
- API and service separation
