#!/bin/bash
set -e

# Update package lists and install PHP, Composer, and related packages
sudo apt-get update
sudo apt-get install -y php php-cli php-mysql php-gd php-mbstring php-curl composer

# Install phpMyAdmin dependencies using composer
if [ -f "pma/composer.json" ]; then
  (cd pma && composer install --no-dev)
fi

# Install JavaScript dependencies for the cluetip plugin
if [ -f "_lib/js/cluetip/package.json" ]; then
  (cd _lib/js/cluetip && npm install)
fi

echo "Setup completed."
