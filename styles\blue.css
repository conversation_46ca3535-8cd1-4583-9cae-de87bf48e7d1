/* Blue Theme Stylesheet */

	.contentcontainer .altheading {background: #00789F url(../img/blue/bg_heading_alt.png); border: 1px solid #00789F; color: #fff; text-shadow: 1px 1px 1px #2D738A;}

	/* Navigation Styling */

	ul#nav li a.heading, a.collapsed, a.expanded, ul.navigation li.selected{border-bottom: 1px solid #006F93; border-top: 1px solid #2DABD5; text-shadow: 1px 1px 1px #0D2C35; background: url(../img/blue/bg_navigation.png) no-repeat top;}

	ul#nav li a.expanded {background: url(../img/blue/bg_navigation.png) 0 -76px;}

	ul#nav li a.collapsed:hover {background: url(../img/blue/bg_navigation.png) no-repeat 0 -38px; width: 205px;}
	ul#nav li a.expanded:hover {background: url(../img/blue/bg_navigation.png) no-repeat 0 -114px; width: 205px;}


	ul.navigation li { border-left: 4px solid #2DABD5; }

	ul.navigation li.selected {background: url(../img/blue/bg_navigation_selected.png) no-repeat -1px 0; border: none; border-left: 4px solid #2DABD5;}



	/* Side Notifications */

	.notifycount {background: url(../img/blue/bg_notify_count.png) no-repeat top;}

	/* Pagination */

    ul.pagination li.page-item.active a {
        background: #00789F;
        border: 1px solid #0D87AE;
        color: #fff;
        font-weight: bold;
    }
    ul.pagination li.page-item.active a:hover {
        background: #005977;
        border: 1px solid #005977;
    }
    ul.pagination li a:hover {
        border-color: #0D87AE;
        background-color: #f0f8ff;
    }
    ul.pagination li.page-item.prev a,
    ul.pagination li.page-item.next a {
        background: #f8f8f8;
    }
    .entries-per-page select {
        border-color: #0D87AE;
    }

	/* Button Styling */

	.btn, .btnalt {background: url(../img/blue/bg_buttons.png) repeat-x top; border: 1px solid #0D87AE!important;
		color: #fff; font-size: 11px; padding: 7px 10px;
		border-radius: 5px; -moz-border-radius: 5px;

		font-weight: 700; border: none; text-decoration:none;}
		.btn:hover {background: url(../img/blue/bg_buttons.png) 0 -50px; border: 1px solid #005977; cursor: pointer;}

	.btnalt {background: url(../img/blue/bg_buttons_alternative.png) repeat-x top !important; border: 1px solid #333 !important; color: #fff; padding: 7px 10px; border: none;}
		.btnalt:hover {background: url(../img/blue/bg_buttons_alternative.png) 0 -50px !important; border: 1px solid #111 !important; cursor: pointer;}




