<?php
/**
 * Application Health Check for LionCRM
 * Verifies core functionality and database connectivity
 */

echo "<h1>LionCRM Health Check</h1>\n";
echo "<p>Verifying application functionality and database connectivity...</p>\n";

$healthChecks = [];
$totalChecks = 0;
$passedChecks = 0;

function healthCheck($checkName, $checkFunction) {
    global $healthChecks, $totalChecks, $passedChecks;
    $totalChecks++;
    
    echo "<h3>Checking: $checkName</h3>\n";
    
    try {
        $result = $checkFunction();
        if ($result['passed']) {
            $passedChecks++;
            echo "<p style='color: green;'>✅ OK: {$result['message']}</p>\n";
        } else {
            echo "<p style='color: red;'>❌ FAIL: {$result['message']}</p>\n";
        }
        $healthChecks[$checkName] = $result;
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ERROR: {$e->getMessage()}</p>\n";
        $healthChecks[$checkName] = ['passed' => false, 'message' => $e->getMessage()];
    }
}

// Check 1: PHP Version and Extensions
healthCheck("PHP Environment", function() {
    $phpVersion = PHP_VERSION;
    $requiredExtensions = ['mysqli', 'session', 'json', 'curl'];
    $missingExtensions = [];
    
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $missingExtensions[] = $ext;
        }
    }
    
    $versionOk = version_compare($phpVersion, '7.4.0', '>=');
    
    if (!$versionOk) {
        return ['passed' => false, 'message' => "PHP version $phpVersion is too old (requires 7.4+)"];
    }
    
    if (!empty($missingExtensions)) {
        return ['passed' => false, 'message' => 'Missing extensions: ' . implode(', ', $missingExtensions)];
    }
    
    return ['passed' => true, 'message' => "PHP $phpVersion with all required extensions"];
});

// Check 2: Configuration Files
healthCheck("Configuration Files", function() {
    $configFile = __DIR__ . '/../_lib/config.inc.php';
    
    if (!file_exists($configFile)) {
        return ['passed' => false, 'message' => 'config.inc.php not found'];
    }
    
    if (!is_readable($configFile)) {
        return ['passed' => false, 'message' => 'config.inc.php is not readable'];
    }
    
    return ['passed' => true, 'message' => 'Configuration file exists and is readable'];
});

// Check 3: Database Connectivity
healthCheck("Database Connection", function() {
    try {
        // Include config to get database credentials
        include_once(__DIR__ . '/../_lib/config.inc.php');
        include_once(__DIR__ . '/../_lib/db.php');
        
        $db = new MySQL();
        $connection = $db->connect();
        
        if (!$connection) {
            return ['passed' => false, 'message' => 'Failed to connect to database'];
        }
        
        // Test a simple query
        $result = $db->query("SELECT 1 as test");
        if (!$result) {
            return ['passed' => false, 'message' => 'Database connected but query failed'];
        }
        
        return ['passed' => true, 'message' => 'Database connection successful'];
    } catch (Exception $e) {
        return ['passed' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
});

// Check 4: File Permissions
healthCheck("File Permissions", function() {
    $directories = [
        __DIR__ . '/../logs',
        __DIR__ . '/../tmp_docs',
        __DIR__ . '/../documents'
    ];
    
    $issues = [];
    
    foreach ($directories as $dir) {
        if (is_dir($dir)) {
            if (!is_writable($dir)) {
                $issues[] = basename($dir) . ' is not writable';
            }
        } else {
            $issues[] = basename($dir) . ' directory does not exist';
        }
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'All directories have proper permissions' : implode(', ', $issues)
    ];
});

// Check 5: Session Functionality
healthCheck("Session Management", function() {
    if (session_status() === PHP_SESSION_NONE) {
        return ['passed' => false, 'message' => 'Sessions are not started'];
    }
    
    if (session_status() === PHP_SESSION_DISABLED) {
        return ['passed' => false, 'message' => 'Sessions are disabled'];
    }
    
    // Test session write
    $_SESSION['health_check'] = 'test';
    if (!isset($_SESSION['health_check'])) {
        return ['passed' => false, 'message' => 'Session write failed'];
    }
    
    unset($_SESSION['health_check']);
    
    return ['passed' => true, 'message' => 'Session management working correctly'];
});

// Check 6: Error Logging
healthCheck("Error Logging", function() {
    $logFile = __DIR__ . '/../logs/php_errors.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        return ['passed' => false, 'message' => 'Log directory does not exist'];
    }
    
    if (!is_writable($logDir)) {
        return ['passed' => false, 'message' => 'Log directory is not writable'];
    }
    
    // Test error logging
    $testMessage = "Health check test - " . date('Y-m-d H:i:s');
    error_log($testMessage, 3, $logFile);
    
    if (file_exists($logFile) && strpos(file_get_contents($logFile), $testMessage) !== false) {
        return ['passed' => true, 'message' => 'Error logging is working correctly'];
    }
    
    return ['passed' => false, 'message' => 'Error logging test failed'];
});

// Check 7: Core Application Files
healthCheck("Core Application Files", function() {
    $coreFiles = [
        __DIR__ . '/../index.php',
        __DIR__ . '/../_lib/db.php',
        __DIR__ . '/../_lib/error_handler.php',
        __DIR__ . '/../controllers/login.php',
        __DIR__ . '/../controllers/client/search.php'
    ];
    
    $missingFiles = [];
    
    foreach ($coreFiles as $file) {
        if (!file_exists($file)) {
            $missingFiles[] = basename($file);
        }
    }
    
    return [
        'passed' => empty($missingFiles),
        'message' => empty($missingFiles) ? 'All core files present' : 'Missing files: ' . implode(', ', $missingFiles)
    ];
});

// Display summary
echo "<hr>\n";
echo "<h2>Health Check Summary</h2>\n";
echo "<p><strong>Total Checks:</strong> $totalChecks</p>\n";
echo "<p><strong>Passed:</strong> $passedChecks</p>\n";
echo "<p><strong>Failed:</strong> " . ($totalChecks - $passedChecks) . "</p>\n";
echo "<p><strong>Health Score:</strong> " . round(($passedChecks / $totalChecks) * 100, 1) . "%</p>\n";

if ($passedChecks == $totalChecks) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL CHECKS PASSED! The application is healthy and ready to use.</p>\n";
} elseif ($passedChecks / $totalChecks >= 0.8) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Most checks passed, but some issues need attention.</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Multiple issues detected. Please address the failures above.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Health check completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
