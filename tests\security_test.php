<?php
/**
 * Security Testing Script for LionCRM
 * Tests for common vulnerabilities and security issues
 */

echo "<h1>LionCRM Security Test Suite</h1>\n";
echo "<p>Testing for security vulnerabilities and code quality issues...</p>\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    $totalTests++;
    
    echo "<h3>Testing: $testName</h3>\n";
    
    try {
        $result = $testFunction();
        if ($result['passed']) {
            $passedTests++;
            echo "<p style='color: green;'>✅ PASSED: {$result['message']}</p>\n";
        } else {
            echo "<p style='color: red;'>❌ FAILED: {$result['message']}</p>\n";
        }
        $testResults[$testName] = $result;
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ERROR: {$e->getMessage()}</p>\n";
        $testResults[$testName] = ['passed' => false, 'message' => $e->getMessage()];
    }
}

// Test 1: Check for SQL Injection vulnerabilities
runTest("SQL Injection Protection", function() {
    $vulnerablePatterns = [
        '/\$_[A-Z]+\[[\'"][^\'"]*[\'"]\]\s*[^;]*;?\s*$/',  // Direct $_POST/$_GET usage
        '/WHERE\s+[^=]*=\s*\$[^;]*[^)]\s*["\']/',          // Direct variable in WHERE
        '/INSERT\s+INTO\s+[^(]*\([^)]*\$[^)]*\)/',         // Direct variable in INSERT
    ];
    
    $files = glob('controllers/**/*.php') + glob('_lib/*.php');
    $issues = [];
    
    foreach ($files as $file) {
        $content = file_get_contents($file);
        foreach ($vulnerablePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $issues[] = $file;
                break;
            }
        }
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'No SQL injection vulnerabilities detected' : 'Potential SQL injection in: ' . implode(', ', $issues)
    ];
});

// Test 2: Check for XSS vulnerabilities
runTest("XSS Protection", function() {
    $files = glob('views/**/*.php') + glob('*.php');
    $issues = [];
    
    foreach ($files as $file) {
        $content = file_get_contents($file);
        // Look for unescaped output
        if (preg_match('/\<\?=\s*\$[^h][^;]*\?>/i', $content) && 
            !preg_match('/htmlspecialchars|htmlentities/', $content)) {
            $issues[] = $file;
        }
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'No obvious XSS vulnerabilities detected' : 'Potential XSS in: ' . implode(', ', $issues)
    ];
});

// Test 3: Check for short PHP tags
runTest("PHP Tag Compliance", function() {
    $files = glob('**/*.php', GLOB_BRACE) + glob('*.php');
    $issues = [];
    
    foreach ($files as $file) {
        $content = file_get_contents($file);
        if (preg_match('/\<\?\s[^p]/', $content)) {
            $issues[] = $file;
        }
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'All files use proper PHP tags' : 'Short PHP tags found in: ' . implode(', ', $issues)
    ];
});

// Test 4: Check for deprecated functions
runTest("Deprecated Function Usage", function() {
    $deprecatedFunctions = [
        'mysql_',
        'addslashes',
        'get_magic_quotes',
        '__autoload',
        'each(',
        'mysqli_list_fields'
    ];
    
    $files = glob('**/*.php', GLOB_BRACE) + glob('*.php');
    $issues = [];
    
    foreach ($files as $file) {
        $content = file_get_contents($file);
        foreach ($deprecatedFunctions as $func) {
            if (strpos($content, $func) !== false) {
                $issues[] = "$file ($func)";
            }
        }
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'No deprecated functions detected' : 'Deprecated functions in: ' . implode(', ', $issues)
    ];
});

// Test 5: Check error logging configuration
runTest("Error Logging Configuration", function() {
    $logDir = __DIR__ . '/../logs';
    $errorLogFile = $logDir . '/php_errors.log';
    $securityLogFile = $logDir . '/security.log';
    
    $issues = [];
    
    if (!is_dir($logDir)) {
        $issues[] = 'Log directory does not exist';
    } elseif (!is_writable($logDir)) {
        $issues[] = 'Log directory is not writable';
    }
    
    if (!ini_get('log_errors')) {
        $issues[] = 'Error logging is not enabled';
    }
    
    return [
        'passed' => empty($issues),
        'message' => empty($issues) ? 'Error logging properly configured' : implode(', ', $issues)
    ];
});

// Test 6: Check for proper input validation
runTest("Input Validation", function() {
    $files = glob('controllers/**/*.php');
    $goodPatterns = [
        'intval(',
        'filter_var(',
        'htmlspecialchars(',
        'real_escape_string(',
    ];
    
    $validatedFiles = 0;
    foreach ($files as $file) {
        $content = file_get_contents($file);
        foreach ($goodPatterns as $pattern) {
            if (strpos($content, $pattern) !== false) {
                $validatedFiles++;
                break;
            }
        }
    }
    
    $percentage = count($files) > 0 ? ($validatedFiles / count($files)) * 100 : 0;
    
    return [
        'passed' => $percentage >= 80,
        'message' => sprintf('%.1f%% of controller files have input validation', $percentage)
    ];
});

// Display summary
echo "<hr>\n";
echo "<h2>Test Summary</h2>\n";
echo "<p><strong>Total Tests:</strong> $totalTests</p>\n";
echo "<p><strong>Passed:</strong> $passedTests</p>\n";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>\n";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>\n";

if ($passedTests == $totalTests) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED! The application appears to be secure.</p>\n";
} else {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Some tests failed. Please review the issues above.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
