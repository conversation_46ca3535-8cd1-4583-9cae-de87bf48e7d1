<? if($no_share): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
				<span>No Share Selected.</span>
				Please select one of the client shares in his <a href="<?=$base?>client/profile/<?=$curr_client->id?>">profile</a>
			</p>
		</div>

<? else: ?>

		<? if(!$template_company_exists): ?>
			<div class="status warning">
				<p>
					<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
					<span>Company template not found.</span>
					No template found for <?=$curr_client->company_name?>. Please contact <PERSON> to create it.
				</p>
			</div>

		<? endif; ?>

		<? if(!$template_registry_exists): ?>
			<div class="status warning">
				<p>
					<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
					<span>Registry template not found.</span>
					No template found for <?=$curr_client->registry_name?>. Please contact Tech to create it.
				</p>
			</div>

		<? endif; ?>

		<!-- Documents -->
		<div class="contentcontainer" id="doc_tabs">



			<div class="headings alt">
                <h2 class="left">Documents</h2>
                <ul class="smltabs">
                	<li><a href="#tab-contract">SPA</a></li>
                	<li><a href="#tab-pout">POUT / SRF</a></li>
                	<li><a href="#tab-nda">NDA</a></li>
                    <li><a href="#tab-ipg">Reg IPG</a></li>
                	<li><a href="#tab-warrants">Warrants</a></li>
                	<li><a href="#tab-bg">BG</a></li>
					<li><a href="#tab-taxes">Taxes</a></li>
                </ul>
            </div>



			<div class="contentbox" id="tab-contract">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="3">Document Options</th>
								</tr>
								<tr>
									<td><h2>Securities Purchase Agreement</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/spa/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/spa/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>


					<li>
						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">Share Description</th>
								</tr>
								<tr>
									<td style="width:240px;"><strong>Registered Share Name:</strong></td>
									<td><?=$share->name?></td>
								</tr>
								<tr>
									<td><strong>Number of Registered Shares:</strong></td>
									<td><?=$share->shares?></td>
								</tr>
								<tr>
									<td><strong>Offered Value per Share:</strong></td>
									<td class="money"><?=$share->price?></td>
								</tr>
								<tr>
									<td><strong>Gross Sale Procceeds:</strong></td>
									<td class="money"><?=$gross_sale_proceeds?></td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">Securities Purchase Agreement Summary</th>
								</tr>
								<tr>
									<td style="width:240px;"><strong>Recertification Deposit Amount per Share:</strong></td>
									<td class="money"><?=$share->recert_amount?></td>
								</tr>
								<tr>
									<td><strong>Recertification Deposit Due:</strong></td>
									<td class="recert_due money">$<?=$recert_deposit_due?></td>
								</tr>
								<tr>
									<td><strong>Gross Sale Proceeds:</strong></td>
									<td class="gross_sales money">$<?=$gross_sale_proceeds?></td>
								</tr>
								<tr>
									<td><strong>Deductible Comission Fee (1.5%):</strong></td>
									<td class="deductible_fee money">$<?=-$deductible_comission_fee ?></td>
								</tr>
								<tr>
									<td><strong>Refundable Recertification Deposit:</strong></td>
									<td class="recert_due money">$<?=$recert_deposit_due?></td>
								</tr>
								<tr>
									<td><strong>Net Sale Proceeds:</strong></td>
									<td class="net_sales money">$<?=$net_sale_proceeds?></td>
								</tr>
							</tbody>
						</table>

						<br />

					</li>
				</ul>

			</div>



			<div class="contentbox" id="tab-pout">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="3">Document Options</th>
								</tr>
								<tr>
									<td><h3>Payout Settlement Agreement</h3></td>
									<td><h3>Release of Pledged Securities Form</h3></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/pout/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/pout/download"/>Download</a>
									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/srf/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/srf/download"/>Download</a>
									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>


				</ul>

			</div>


			<div class="contentbox" id="tab-nda">


				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<? if($no_ref): ?>

						<div class="status warning">
				        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
							<p>
								<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
								<span>No referral selected for this client.</span>
								Please select one in his <a href="<?=$base?>client/profile/<?=$curr_client->id?>">profile</a>
							</p>
						</div>

				<? else: ?>

				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Mutual Non-Disclosure Agreement</th>
						</tr>
						<tr>
							<td style="width:100px;"><strong>Selected Referall:</strong></td>
							<td><?=$curr_client->ref_name?></td>
						</tr>
						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/nda/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/nda/download"/>Download</a>
							</td>
						</tr>
					</tbody>
				</table>

				<? endif; ?>


			</div>

			<div class="contentbox" id="tab-ipg">


				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Insured Payment Guarantee</th>
						</tr>
						<tr>
							<td style="width:100px;"><strong>Current Registry:</strong></td>
							<td><?=$curr_client->registry_name?></td>
						</tr>

						<tr>
							<td  style="width:100px;"><strong>SPA sent at:</strong></td>
							<td>
								<?php if($spa_not_sent):?>

								Couldn't find a date or SPA not sent. Please, select "SPA Sent" status on client profile.

								<?php else: ?>

								<input type="hidden" id="spa_sent_id" value="<?=intval($spa_sent_id)?>" />
								<input class="inputbox" style="width:110px;" id="spa_sent" value="<?=htmlspecialchars($date_spa_db)?>" />
								<em>(yyyy-mm-dd)</em>&nbsp;
								<div id="msg_updating_spa"></div>

								<?php endif; ?>

							</td>
						</tr>
						<tr>
							<td colspan=2>
								<?php $onclick = "window.open('{$base}client/documents/ipg/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/ipg/download"/>Download</a>
								<br><br><br>
							</td>
						</tr>

						<tr>
							<th colspan="2">Stamped Version - "Subject to Signed Exhibit A"</th>
						</tr>

						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/ipg-stamped/preview');"; ?>
								<input type="button" class="btn" value="Preview Stamped" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/ipg-stamped/download"/>Download Stamped</a>
							</td>
						</tr>

					</tbody>
				</table>

			</div>


<? //  - SPA SENT AT: (<?=$spa_sent ?>
			<div class="contentbox" id="tab-warrants">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
						<table width="600">
							<tbody>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="warrants_info"></div>
									</td>

								</tr>
								<tr>
									<th colspan="2">
										Warrants Info
									</th>

								</tr>
								<tr>
									<td  style="width:100px;"><strong>Warrants:</strong></td>
									<td><input class="inputbox minibox" id="share_warrants"
										value="<?=$share->warrants;?>" /></td>
								</tr>
								<tr>
									<td><strong>Warrant price:</strong></td>
									<td><input class="inputbox minibox money" id="share_warrant_price"
										value="<?=$share->warrant_price;?>" /></td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<td><h2>Warrants - Company Exhibit C</h2></td>
									<td><h2>Warrants - Registry Letter</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/warrants_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/warrants_co/download"/>Download</a>

									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/warrants_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/warrants_reg/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>



				</ul>

			</div>



			<div class="contentbox" id="tab-bg">


				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>

				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Bonded Guarantee</th>
						</tr>
						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/bonded_guarantee/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/bonded_guarantee/download"/>Download</a>
							</td>
						</tr>
					</tbody>
				</table>



			</div>





			<div class="contentbox" id="tab-taxes">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
<br>
						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">
										QTAX - Quarterly Tax
									</th>

								</tr>
								<tr>
									<td>
										<strong>Registry Letter</strong><br>
										<? $onclick = "window.open('{$base}client/documents/qtax_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/qtax_reg/download"/>Download</a>


									</td>


									<td>
										<strong>Company Exhibit D</strong><br>
										<? $onclick = "window.open('{$base}client/documents/qtax_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/qtax_co/download"/>Download</a>

									</td>


								</tr>
								<tr><th colspan="2"></th></tr>

							</tbody>
						</table>
						<br>


					</li>


					<li>
					<br><br>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">
										SATAX - Semi-Annual Tax
									</th>
								</tr>

								<tr>
									<td>
										<strong>Registry Letter</strong><br>
										<? $onclick = "window.open('{$base}client/documents/satax_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/satax_reg/download"/>Download</a>


									</td>


									<td>
										<strong>Company Exhibit E</strong><br>
										<? $onclick = "window.open('{$base}client/documents/satax_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/satax_co/download"/>Download</a>

									</td>

								</tr>
								<tr><th colspan="2"></th></tr>



							</tbody>
						</table>
						<br>


					</li>



				</ul>

			</div>

























        </div>

		<br />


<? endif; //noclient?>