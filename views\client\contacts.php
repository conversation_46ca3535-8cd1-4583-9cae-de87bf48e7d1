

<? if($msg): ?>

		<div class="status info">
        	<p class="closestatus"><a href="<?=$base?>client/contacts" title="Close">x</a></p>
			<p>
				<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
				<span><?=$msg?></span>
			</p>
		</div>

<? endif; ?>




	<!-- CONTAINER client Phones -->
	<div class="contentcontainer small left" >

	    <div class="headings alt">
	        <h2 class="left"><?=$curr_client->name?> Contacts</h2>
	    </div>

		<div class="contentbox">

			<? $form_action = ($subaction == 'editPhone')? "editPhone/$phone->id" : 'addPhone'; ?>

			<form method='post' action="<?=$base?>client/contacts/<?=$form_action?>">

				<input type="hidden" name="sent_phone" value="true">


				<table>
					<tbody>
						<tr>
							<td><strong>Phone:</strong></td>
							<td><input name="fields_phone[contact]" type="text" class="inputbox medbox" value="<?=$phone->contact?>" /></td>
							<td><strong>Type:</strong></td>
							<td>
								<select name="fields_phone[id_phone_type]">
									<?=$so['id_phone_type']?>
								</select>
							</td>

						</tr>
						<tr>
							<? $bt_value = ($subaction == 'editPhone') ? 'Update and Save Changes' : 'Add new Phone'; ?>

							<td colspan=4>
								<input type="submit" class="btn" value="<?=$bt_value?>" />

								<? if($subaction == 'editPhone'): ?>

								<a class="btn" style="color:white;" href="<?="{$base}client/contacts";?>">Cancel</a>
								<a class="btn" style="color:white;" href="<?="{$base}client/contacts/deletePhone/$phone->id";?>">Delete</a>

								<? endif;?>

							</td>
						</tr>

					</tbody>
				</table>

				<br />

			</form>

	    	<table width="100%">
	        	<thead>
	            	<tr>
	                    <th>Phone</th>
	                    <th>Type</th>
	                </tr>
	            </thead>
	            <tbody>

	            	<? if(isset($phones) && is_array($phones) && count($phones) > 0): ?>

						<? foreach($phones as $phone):?>

						<? $onclick = "location.href = '{$base}client/contacts/editPhone/{$phone->id}';"; ?>

						<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
							<td><?=$phone->contact ?></td>
							<td><?=$phone->phone_type ?></td>
						</tr>

						<? endforeach; ?>

					<? else: ?>

						<tr>
							<td colspan="2">

	    						<div class="status info">
									<p>
										<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
										<span>No results found:</span>
										Make sure you have the right priveleges and all words are spelled correctly.
									</p>
								</div>

							</td>
						</tr>

					<? endif;?>


	            </tbody>
	        </table>

	    </div>

	</div>




	<!-- CONTAINER client E-mails -->
	<div class="contentcontainer small left" style='margin-left:40px;'>

	    <div class="headings alt">
	        <h2 class="left">E-mails</h2>
	    </div>

		<div class="contentbox">
		<?	//printr($employee);?>

		<? $form_action = ($subaction == 'editEmail')? "editEmail/$email->id" : 'addEmail'; ?>

		<form method='post' action="<?=$base?>client/contacts/<?=$form_action?>">

			<input type="hidden" name="sent_email" value="true">


			<table>
				<tbody>
					<tr>
						<td><strong>Email:</strong></td>
						<td><input name="fields_email[contact]" type="text" class="inputbox medbox" value="<?=$email->contact?>" /></td>

					</tr>
					<tr>
						<? $bt_value = ($subaction == 'editEmail') ? 'Update and Save Changes' : 'Add new E-mail'; ?>

						<td colspan=4>
							<input type="submit" class="btn" value="<?=$bt_value?>" />

							<? if($subaction == 'editEmail'): ?>

							<a class="btn" style="color:white;" href="<?="{$base}client/contacts";?>">Cancel</a>
							<a class="btn" style="color:white;" href="<?="{$base}client/contacts/deleteEmail/$email->id";?>">Delete</a>

							<? endif;?>

						</td>
					</tr>

				</tbody>
			</table>

			<br />

		</form>

	    	<table width="100%">
	        	<thead>
	            	<tr>
	                    <th>Email</th>
	                </tr>
	            </thead>
	            <tbody>

	            	<? if(isset($emails) && is_array($emails) && count($emails) > 0): ?>

						<? foreach($emails as $email):?>

						<? $onclick = "location.href = '{$base}client/contacts/editEmail/{$email->id}';"; ?>

						<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
							<td><?=$email->contact ?></td>
						</tr>

						<? endforeach; ?>

					<? else: ?>

						<tr>
							<td colspan="2">

	    						<div class="status info">
									<p>
										<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
										<span>No results found</span>
									</p>
								</div>

							</td>
						</tr>

					<? endif;?>


	            </tbody>
	        </table>

	    </div>

	</div>





<br />