<? if($no_share || $no_company): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">

				<? if($no_share): ?>

				<span>No Share Selected.</span>
				Please select one of the client shares in his <a href="<?=$base?>client/profile/<?=$curr_client->id?>">profile</a>

				<? endif; ?>

				<? if($no_company): ?>

				<span>No Company Selected.</span>
				The client needs to be assigned to a company and agent.

				<? endif; ?>
			</p>
		</div>

<? else: ?>

		<? if(!$template_company_exists): ?>
			<div class="status warning">
				<p>
					<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
					<span>Company template not found.</span>
					No template found for <?=$curr_client->company_name?>. Please contact Tech to create it.
				</p>
			</div>

		<? endif; ?>

		<? if(!$template_registry_exists): ?>
			<div class="status warning">
				<p>
					<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
					<span>Registry template not found.</span>
					No template found for <?=$curr_client->registry_name?>. Please contact Tech to create it.
				</p>
			</div>

		<? endif; ?>

		<div id="msg_status_docs" class="status info" style="display: none;"></div>

		<!-- Documents -->
		<div class="contentcontainer" id="doc_tabs">



			<div class="headings alt">
                <h2 class="left">Documents</h2>
                <ul class="smltabs">
                	<li><a href="#tab-contract">SPA</a></li>
                	<li><a href="#tab-margin">Margin</a></li>
                	<li><a href="#tab-pout">POUT / SRF</a></li>
                	<li><a href="#tab-nda">NDA</a></li>
                    	<li><a href="#tab-ipg">Reg IPG</a></li>
                	<li><a href="#tab-warrants">Warrants</a></li>
                	<li><a href="#tab-bg">BG</a></li>
			<li><a href="#tab-taxes">Taxes</a></li>
                	<li><a href="#tab-addshares">Add. Shares</a></li>
			<li><a href="#tab-defnotice">Def. Notice</a></li>
			<li><a href="#tab-messages">E-mail Msgs</a></li>
                </ul>
            </div>



			<div class="contentbox" id="tab-contract">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="3">Document Options</th>
								</tr>
								<tr>
									<td><h2>Securities Purchase Agreement</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/spa/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/spa/download"/>Download</a>

										<? $onclick = "sendDoc('SPA')"; ?>
										<input type="button" class="btn" id="btn_send_SPA" value="Send SPA" onclick="<?=$onclick?>"/>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>


					<li>
						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">Share Description</th>
								</tr>
								<tr>
									<td style="width:240px;"><strong>Registered Share Name:</strong></td>
									<td><?=$share->name?></td>
								</tr>
								<tr>
									<td><strong>Number of Registered Shares:</strong></td>
									<td><?=$share->shares?></td>
								</tr>
								<tr>
									<td><strong>Offered Value per Share:</strong></td>
									<td class="money"><?=$share->price?></td>
								</tr>
								<tr>
									<td><strong>Gross Sale Procceeds:</strong></td>
									<td class="money"><?=$gross_sale_proceeds?></td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">Securities Purchase Agreement Summary</th>
								</tr>
								<tr>
									<td style="width:240px;"><strong>Recertification Deposit Amount per Share:</strong></td>
									<td class="">$<?=$share->recert_amount?></td>
								</tr>
								<tr>
									<td><strong>Recertification Deposit Due:</strong></td>
									<td class="recert_due money">$<?=$recert_deposit_due?></td>
								</tr>
								<tr>
									<td><strong>Gross Sale Proceeds:</strong></td>
									<td class="gross_sales money">$<?=$gross_sale_proceeds?></td>
								</tr>
								<tr>
									<td><strong>Deductible Comission Fee (1.5%):</strong></td>
									<td class="deductible_fee money">$<?=-$deductible_comission_fee ?></td>
								</tr>
								<tr>
									<td><strong>Refundable Recertification Deposit:</strong></td>
									<td class="recert_due money">$<?=$recert_deposit_due?></td>
								</tr>
								<tr>
									<td><strong>Net Sale Proceeds:</strong></td>
									<td class="net_sales money">$<?=$net_sale_proceeds?></td>
								</tr>
							</tbody>
						</table>

						<br />

					</li>
				</ul>

			</div>


			<div class="contentbox" id="tab-margin">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
						<table width="600">
							<tbody>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="margin_open_info"></div>
									</td>

								</tr>
								<tr>
									<td  style="width:115px;" colspan="2">

										<strong>Seller has placed a marginal rate of:</strong>

										<input class="inputbox minibox" id="share_margin_open"
										value="<?=$share->margin_open;?>"/>%

										<br>
										Company has agreed to be financially responsible to the remaining <strong><span id="share_margin_company"><?=(100-$share->margin_open)?></span>%</strong> margin facility
									</td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<td><h2>Margin Agreement and Revised Exhibit A</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/margin_open/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/margin_open/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>


				</ul>

			</div>



			<div class="contentbox" id="tab-pout">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">Document Options</th>
								</tr>
								<tr>
									<td><h3>Payout Settlement Agreement</h3></td>
									<td><h3>Release of Pledged Securities Form</h3></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/pout/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/pout/download"/>Download</a>
									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/srf/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
										<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/srf/download"/>Download</a>
									</td>
								</tr>


								<tr>
									<td colspan="2"><h3>Send Documents</h3></td>
								</tr>
								<tr>
									<td colspan="2">
										<? $onclick = "sendDoc('POUT')"; ?>
										<input type="button" class="btn" id="btn_send_POUT" value="Send POUT" onclick="<?=$onclick?>"/>

										&nbsp;&nbsp;&nbsp;&nbsp;

										<? $onclick = "sendDoc('POUT+SRF')"; ?>
										<input type="button" class="btn" id="btn_send_POUT+SRF" value="Send POUT + SRF together" onclick="<?=$onclick?>"/>
									</td>
									<td>
									</td>
								</tr>


							</tbody>
						</table>
						<br>


					</li>


				</ul>

			</div>


			<div class="contentbox" id="tab-nda">


				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<? if($no_ref): ?>

						<div class="status warning">
				        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
							<p>
								<img alt="Warning" src="<?=$base?>img/icons/icon_warning.png">
								<span>No referral selected for this client.</span>
								Please select one in his <a href="<?=$base?>client/profile/<?=$curr_client->id?>">profile</a>
							</p>
						</div>

				<? else: ?>

				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Mutual Non-Disclosure Agreement</th>
						</tr>
						<tr>
							<td style="width:100px;"><strong>Selected Referall:</strong></td>
							<td><?=$curr_client->ref_name?></td>
						</tr>
						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/nda/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/nda/download"/>Download</a>

								<? $onclick = "sendDoc('NDA')"; ?>
								<input type="button" class="btn" id="btn_send_NDA" value="Send NDA" onclick="<?=$onclick?>"/>

							</td>
						</tr>
					</tbody>
				</table>

				<? endif; ?>


			</div>

			<div class="contentbox" id="tab-ipg">


				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Insured Payment Guarantee</th>
						</tr>
						<tr>
							<td style="width:100px;"><strong>Current Registry:</strong></td>
							<td><?=$curr_client->registry_name?></td>
						</tr>

						<tr>
							<td  style="width:100px;"><strong>Document Date:</strong></td>
							<td>
								<input class="inputbox" style="width:110px;" id="share_ipg_date" value="<?=$share->ipg_date?>" />
								<em>(yyyy-mm-dd)</em>&nbsp;
								<div id="msg_updating_ipg_date"></div>
							</td>
						</tr>


						<tr>
							<td  style="width:100px;"><strong>SPA sent at:</strong></td>
							<td>
								<? if($spa_not_sent):?>

								Couldn't find a date or SPA not sent. Please, select "SPA Sent" status on client profile.

								<? else: ?>

								<input type="hidden" id="spa_sent_id" value="<?=$spa_sent_id?>" />
								<input class="inputbox" style="width:110px;" id="spa_sent" value="<?=$date_spa_db?>" />
								<em>(yyyy-mm-dd)</em>&nbsp;
								<div id="msg_updating_spa"></div>

								<? endif; ?>

							</td>
						</tr>



						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/ipg/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/ipg/download"/>Download</a>
								<br><br><br>
							</td>
						</tr>

						<tr>
							<th colspan="2">Stamped Version - "Subject to Signed Exhibit A"</th>
						</tr>

						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/ipg-stamped/preview');"; ?>
								<input type="button" class="btn" value="Preview Stamped" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/ipg-stamped/download"/>Download Stamped</a>
							</td>
						</tr>

					</tbody>
				</table>

			</div>


<? //  - SPA SENT AT: (<?=$spa_sent ?>
			<div class="contentbox" id="tab-warrants">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
						<table width="600">
							<tbody>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="warrants_info"></div>
									</td>

								</tr>
								<tr>
									<th colspan="2">
										Warrants Info
									</th>

								</tr>
								<tr>
									<td  style="width:100px;"><strong>Warrants:</strong></td>
									<td><input class="inputbox minibox" id="share_warrants"
										value="<?=$share->warrants;?>" /></td>
								</tr>
								<tr>
									<td><strong>Warrant price:</strong></td>
									<td>$<input class="inputbox minibox" id="share_warrant_price"
										value="<?=$share->warrant_price;?>" /></td>
								</tr>
							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<td><h2>Warrants - Company Exhibit C</h2></td>
									<td><h2>Warrants - Registry Letter</h2></td>
								</tr>
								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/warrants_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/warrants_co/download"/>Download</a>

									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/warrants_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/warrants_reg/download"/>Download</a>

									</td>
								</tr>


								<tr>
									<td></td>
									<td><h2>Warrants - Registry Letter 2</h2></td>
								</tr>
								<tr>
									<td>
									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/warrants_reg2/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/warrants_reg2/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>



					<li>
						<table width="600">

								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="warrants_margin"></div>
									</td>

								</tr>
								<tr>
									<th colspan="2">
										Margin on Warrants
									</th>

								</tr>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="margin_warrants_info"></div>
									</td>

								</tr>
								<tr>
									<td  style="width:115px;"><strong>Margin Percentage:</strong></td>
									<td><input class="inputbox minibox" id="share_margin_warrants"
										value="<?=$share->margin_warrants;?>" />%</td>
								</tr>

						</table>
						<br />
						<br />
					</li>


					<li>
						<table width="600">
							<tbody>
								<tr>
									<td><h2>Warrants - Margin Agreement and Revised Exhibit C</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/margin_warrants/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/margin_warrants/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>
					</li>


				</ul>

			</div>



			<div class="contentbox" id="tab-bg">


				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>

				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Bonded Guarantee</th>
						</tr>
						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/bonded_guarantee/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/bonded_guarantee/download"/>Download</a>
							</td>
						</tr>
					</tbody>
				</table>



			</div>





			<div class="contentbox" id="tab-taxes">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
<br>
						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">
										QTAX - Quarterly Tax
									</th>

								</tr>
								<tr>
									<td>
										<strong>Registry Letter</strong><br>
										<? $onclick = "window.open('{$base}client/documents/qtax_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/qtax_reg/download"/>Download</a>


									</td>


									<td>
										<strong>Company Exhibit D</strong><br>
										<? $onclick = "window.open('{$base}client/documents/qtax_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/qtax_co/download"/>Download</a>

									</td>


								</tr>
								<tr><th colspan="2"></th></tr>

							</tbody>
						</table>
						<br>


					</li>


					<li>
					<br><br>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">
										SATAX - Semi-Annual Tax
									</th>
								</tr>

								<tr>
									<td>
										<strong>Registry Letter</strong><br>
										<? $onclick = "window.open('{$base}client/documents/satax_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/satax_reg/download"/>Download</a>


									</td>


									<td>
										<strong>Company Exhibit D</strong><br>
										<? $onclick = "window.open('{$base}client/documents/satax_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/satax_co/download"/>Download</a>

									</td>

								</tr>
								<tr><th colspan="2"></th></tr>



							</tbody>
						</table>
						<br>


					</li>

					<li>
					<br><br>

						<table width="600">
							<tbody>
								<tr>
									<th colspan="2">
										Full Tax
									</th>
								</tr>

								<tr>
									<td>
										<strong>Registry Letter</strong><br>
										<? $onclick = "window.open('{$base}client/documents/fulltax_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/fulltax_reg/download"/>Download</a>


									</td>


									<td>
										<strong>Company Exhibit D</strong><br>
										<? $onclick = "window.open('{$base}client/documents/fulltax_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/fulltax_co/download"/>Download</a>

									</td>

								</tr>
								<tr><th colspan="2"></th></tr>



							</tbody>
						</table>
						<br><br>
					</li>


					<li>
						<table width="600">
								<tr>
									<th colspan="2">
										Margin on Taxes
									</th>

								</tr>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="margin_tax_info"></div>
									</td>

								</tr>
								<tr>
									<td  style="width:115px;"><strong>Margin Percentage:</strong></td>
									<td><input class="inputbox minibox" id="share_margin_tax"
										value="<?=$share->margin_tax;?>" />%</td>
								</tr>
						</table>
					</li>

					<li>
						<table width="600">
								<tr>
									<td colspan="3"><h2>Taxes - Margin Agreement and Revised Exhibit D</h2></td>
								</tr>

								<tr>
									<td><strong>For QTAX</strong></td>
									<td><strong>For SATAX</strong></td>
									<td><strong>For Full Tax</strong></td>
								</tr>

								<tr>
									<td>
										<a class="btn" style="color:white" target="_blank"
											href="<?=$base?>client/documents/qtax_margin/preview"/>Preview</a>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/qtax_margin/download"/>Download</a>

									</td>
									<td>
										<a class="btn" style="color:white"  target="_blank"
											href="<?=$base?>client/documents/satax_margin/preview"/>Preview</a>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/satax_margin/download"/>Download</a>

									</td>
									<td>
										<a class="btn" style="color:white"  target="_blank"
											href="<?=$base?>client/documents/fulltax_margin/preview"/>Preview</a>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/fulltax_margin/download"/>Download</a>

									</td>
								</tr>
						</table>
						<br>
					</li>



				</ul>

			</div>








			<div class="contentbox" id="tab-addshares">

				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<ul>
					<li>
						<table width="600">
							<tbody>
								<tr>
									<td colspan="2" style="text-align: right;">
										<div id="addshares_info"></div>
									</td>

								</tr>
								<tr>
									<th colspan="2">
										Additional Shares
									</th>

								</tr>
								<tr>
									<td  style="width:145px;"><strong>Additional shares:</strong></td>
									<td><input class="inputbox medbox" id="share_addshares_amount"
										value="<?=$share->addshares_amount;?>" /></td>
								</tr>

								<tr>
									<td><strong>Price per share:</strong></td>
									<td><input class="inputbox minibox money" id="share_addshares_price"
										value="<?=$share->addshares_price;?>" /></td>
								</tr>

								<tr>
									<td><strong>Amount due:</strong></td>
									<td>
										<span class="money" id="share_addshares_due"></span>
									</td>
								</tr>

								<tr>
									<td><strong>Clients Left:</strong></td>
									<td><input class="inputbox minibox" id="share_addshares_clients"
										value="<?=$share->addshares_clients;?>" /></td>
								</tr>

								<tr>
									<td><strong>Defaulted Amount:</strong></td>
									<td>
										<span id="share_addshares_total"></span>
									</td>
								</tr>


<?
/*

Total defaulted shares
Clients Left

Price per Share
Total Due

|ADDITIONAL_SHARES_CLIENTS|
|ADDITIONAL_SHARES_TOTAL|
|ADDITIONAL_SHARES_EACH|
|ADDITIONAL_SHARES_PRICE|
|ADDITIONAL_SHARES_DUE|
*/
?>


							</tbody>
						</table>
						<br />
						<br />

					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<td><h2>Add Shares - Company Exhibit D</h2></td>
									<td><h2>Add Shares - Registry Letter</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/addshares_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/addshares_co/download"/>Download</a>

									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/addshares_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/addshares_reg/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>

					<li>

						<table width="600">
							<tbody>
								<tr>
									<td><h2>Liens - Company Exhibit E</h2></td>
									<td><h2>Liens - Registry Letter</h2></td>
								</tr>

								<tr>
									<td>
										<? $onclick = "window.open('{$base}client/documents/liens_co/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/liens_co/download"/>Download</a>

									</td>
									<td>
										<? $onclick = "window.open('{$base}client/documents/liens_reg/preview');"; ?>
										<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>

										<a class="btn doc_link" style="color:white"
											href="<?=$base?>client/documents/liens_reg/download"/>Download</a>

									</td>
								</tr>
							</tbody>
						</table>
						<br>


					</li>



				</ul>

			</div>

			<div class="contentbox" id="tab-defnotice">
				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
				<table width="400">
					<tbody>
						<tr>
							<th colspan="2">Default Notice</th>
						</tr>
						<tr>
							<td colspan=2>
								<? $onclick = "window.open('{$base}client/documents/default_notice/preview');"; ?>
								<input type="button" class="btn" value="Preview" onclick="<?=$onclick?>"/>
								<a class="btn doc_link" style="color:white" href="<?=$base?>client/documents/default_notice/download"/>Download</a>

								<?php $onclick = "sendDoc('default_notice')";?>
								<input type="button" class="btn" id="btn_send_default_notice" value="Send Default Notice" onclick="<?=$onclick?>"/>

							</td>
						</tr>
					</tbody>
				</table>
			</div>


			<div class="contentbox" id="tab-messages">
				<h2><?=$curr_client->title?> <?=$curr_client->name?></h2>
				<br>
					<?php
						$msg_buttons = [
							'reg_info' => 'Registry Contact Info',
							'htr1' => 'Telephone Appointment',
							'htr2' => 'HTR - discuss pout',
							'htr3' => 'HTR - let me know',
							'procedure_letter' => 'Procedure Letter',
							'contact_details' => 'Contact Details',
							'unsuccessful_contact' => 'Unsuccessful contact'
						];
					?>

					<table width="800">
					<tbody>
						<tr>
							<th>Send E-mail Messages</th>
						</tr>
						<tr>
							<td>

							<form method="post" enctype="multipart/form-data" action="<?=$base?>ajax/sendMessage/" id="form_msg">
								<select name="which" class="inputbox" style="width: 100%;" onchange="loadMessage(this.value);">
									<option value="custom">Select a saved reply or write a custom one</option>
									<?php foreach($msg_buttons as $msg_which => $msg_label):?>
										<option value="<?=$msg_which?>"><?=$msg_label?></option>
									<?php endforeach; ?>
								</select>

								<hr style="margin: 10px 0px;">

								<table>

									<tr>
										<td>From:</td>
										<td><?=$curr_client->company_agent_name?> (<?=$curr_client->company_agent_email?>)</td>
									</tr>
									<tr>
										<td>Subject</td>
										<td><input id="msg_subject" name="msg_subject" class="inputbox" style="width: 780px; margin-bottom: 10px;"></td>
									</tr>
									<tr>
										<td>Message</td>
										<td>
											<textarea id="msg_body" name="msg_body" class="ckeditor" style="height: 350px"></textarea>
										</td>
									</tr>

									<tr>
										<td>Atts.</td>
										<td>
											<input name="att1" type="file" class="inputbox"><br>
											<input name="att2" type="file" class="inputbox"><br>
											<input name="att3" type="file" class="inputbox"><br>
											<input name="att4" type="file" class="inputbox"><br>
											<input name="att5" type="file" class="inputbox"><br>
											<input name="att6" type="file" class="inputbox">
										</td>
									</tr>
								</table>

							<!--
								<?php foreach($msg_buttons as $msg_which => $msg_label):?>
									<div style="float: left; margin-bottom:15px; margin-right:15px; padding: 15px; border: 1px solid #eee;">
										<strong><?=$msg_label?></strong><br>
										<input type="button" class="btn" value="Preview" onclick="sendMessage('<?=$msg_which?>','preview', '');"/>
										<input type="button" class="btn" style="margin:10px;" value="Send" onclick="sendMessage('<?=$msg_which?>','send','<?=$msg_label?>');"/>
									</div>
								<?php endforeach; ?>
							-->

								<input id="msg_sendbtn" type="button" class="btn" style="width: 200px; float:right; margin:10px;" value="Send Message"
									   onclick="checkSendMessage();"/>

							</form>
							<br style="clear: both;">
							<div id="msg_status" style="float:right; padding: 5px; padding-left: 0px; font-weight: bold;"></div>

							</td>
						</tr>
					</tbody>
				</table><br>



			<!--
				<div id="msg_status" style="padding: 5px; padding-left: 0px; font-weight: bold;"></div>

				<div id="msg_preview" style="display: none; width: 780px;">
					<div id="msg_subject" name="msg_subject" class="inputbox" style="width:100%;">Select messsage above.</div>
					<div id="msg_body" name="msg_body" class="inputbox" style="width:100%; min-height:100px;"></div>
				</div>
			-->
			</div>
  </div>

		<br />

		<?php
			if($curr_client->id){
				$query = "SELECT * from client_contacts where id_client = $curr_client->id and contact_type = 'email'";
				$emails = $db->getQueryList($query);
			}
		?>
		<!-- Client E-mails -->
		<div class="contentcontainer sml left">
		    <div class="headings alt">
                <h2 class="left">Select Client E-mail</h2>
            </div>
			<div class="contentbox">
            	<table width="100%">
                    <tbody>
						<tr class='alt'>
                        	<td><strong>E-mail(s)</strong></td>
                        	<td>
								<? if(isset($emails) && is_array($emails) && count($emails) > 0): foreach($emails as $email): ?>
								<input name="client_emails" type="radio" value="<?=$email->contact?>" />&nbsp;<?=$email->contact?><br>
								<? endforeach; endif;?>
							</td>
						</tr>
                    </tbody>
                </table>
			</div>
        </div>



<? endif; //noclient?>
