		<? if($msg): ?>
			<div class="status info">
				<p class="closestatus"><a href="<?=$base?>" title="Close">x</a></p>
				<p>
					<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
					<span><?=$msg?></span>
				</p>
			</div>
		<? endif; ?>



		<div style="float: right; margin-top: 8px; margin-right: 15px;"><input type="button" value="Check for new messages" class="btn" onclick="window.location.reload();" /></div>
		<!-- Content Box Start -->
		<div class="contentcontainer">

			<div class="headings alt">
				<h2>Client Messages</h2>
			</div>

			<div class="contentbox">
				<table width="100%" id="tbl_messages">
					<thead>
						<tr>
							<th style="width: 100px;">Date</th>
							<th>Subject</th>
							<th style="width: 300px;">From</th>
							<th style="width: 350px;">To</th>
							<th style="width: 10px; text-align: right;">X</th>
						</tr>
					</thead>
					<tbody>

						<? if(isset($emails) && is_array($emails) && count($emails) > 0): ?>

							<? foreach($emails as $email):?>

							<?php $not_read = ($email->is_read == 'no') ? 'not-read' : '';?>
							<?php $onclick = "location.href = '{$base}client/messages/" . intval($email->id) . "';"; ?>
							<tr class="<?=htmlspecialchars($not_read)?>" style="border-bottom:1px solid #999;">
								<td onclick="<?=$onclick?>"><?=htmlspecialchars($email->date)?></td>
								<td onclick="<?=$onclick?>" class="subject">

									<?php if($email->attachs): ?>
										<?php $icon = ($email->is_voicemail=='yes') ? 'speaker' : 'attach';  ?>

										<img src="<?=$base?>img/icons/<?=htmlspecialchars($icon)?>.png" style="width: 14px;">

									<?php endif;?>
									<?=htmlspecialchars($email->subject)?>
								</td>
								<td onclick="<?=$onclick?>"><?=htmlspecialchars($email->client_name)?> (<?=htmlspecialchars($email->email_from)?>)</td>
								<td onclick="<?=$onclick?>"><?=htmlspecialchars($email->email_to)?></td>
								<td style="text-align: right;">
										<a class="msg_remove" href="<?=$base?>client/messages/<?=intval($email->id)?>/remove"
											onclick="return confirm('Delete this message?');">X</a>
								</td>
							</tr>

							<?php endforeach; ?>

						<?php else: ?>

							<tr>
								<td colspan="5">

									<div class="status info">
										<p>
											<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
											<span>No results found:</span>
											Make sure you have the right priveleges and all words are spelled correctly.
										</p>
									</div>

								</td>
							</tr>

						<?php endif;?>
					</tbody>

				</table>

				<div class="spacer"></div>
				<div style="clear: both;"></div>
			</div>

		</div>
		<!-- Content Box End -->

		<style type="text/css">
			.not-read{
				background: rgba(2, 2, 255, 0.05) !important;
			}

			.not-read .subject{
				font-weight: bold;
				text-shadow: 1px 1px 1px #fff;
			}
			.msg_remove{
				border: 1px solid #900;
				background-color: #FCC;
				color: #900 !important;
				text-decoration: none;
				font-weight: bold;
				padding: 4px;
				text-align: right;
			}

			#tbl_messages_filter{ margin-bottom: 15px; }
		</style>

