		<? if($msg): ?>
			<div class="status info">
				<p class="closestatus"><a href="<?=$base?>" title="Close">x</a></p>
				<p>
					<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
					<span><?=$msg?></span>
				</p>
			</div>
		<? endif; ?>

		<!-- Content Box Start -->
		<div class="contentcontainer">

			<div class="headings alt">
				<h2>Sent Messages</h2>
			</div>

			<div class="contentbox">

				<table width="100%" id="tbl_messages">
					<thead>
						<tr>
							<th style="width: 100px;">Date</th>
							<th>Subject</th>
							<th style="width: 190px;">From</th>
							<th style="width: 350px;">To</th>
							<th style="width: 10px; text-align: right;">X</th>
						</tr>
					</thead>
					<tbody>

						<? if(isset($emails) && is_array($emails) && count($emails) > 0): ?>

							<? foreach($emails as $email):?>

							<tr class="msg-row details-control"  data-msgid=<?=$email->id?>>
								<td><?=$email->date?></td>
								<td class="subject">
									<?php if($email->attachs): ?>
										<img src="<?=$base?>img/icons/attach.png" style="width: 14px;">
									<?php endif;?>
									<?=$email->subject?>
								</td>
								<td><?=$email->email_from?></td>
								<td><?=$email->email_to?> (<?=$email->id_client?> - <?=$email->client_name?>)</td>
								<td style="text-align: right;"><a class="msg-open" href="#">open</a></td>
							</tr>

							<? endforeach; ?>

						<? else: ?>

							<tr>
								<td colspan="5">

									<div class="status info">
										<p>
											<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
											<span>No results found:</span>
											Make sure you have the right priveleges and all words are spelled correctly.
										</p>
									</div>

								</td>
							</tr>

						<? endif;?>
					</tbody>

				</table>

				<div class="spacer"></div>
				<div style="clear: both;"></div>
			</div>

		</div>
		<!-- Content Box End -->

		<style type="text/css">
			.msg-open{
				border: 1px solid #009;
				background-color: #CCF;
				color: #009 !important;
				text-decoration: none;
				font-weight: bold;
				padding: 5px;
				text-align: right;
			}
			.msg-row{ line-height: 30px; }
			.message{ border-left: 3px solid #DDD; border-bottom: 3px solid #DDD; padding-bottom: 25px; padding-left: 10px;}
			#tbl_messages_filter{ margin-bottom: 15px; }

		</style>
