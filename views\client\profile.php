
<? if($no_client): ?>

		<div class="status warning">
        	<p class="closestatus"><a href="<?=$base?>client/search" title="Close">x</a></p>
			<p>
				<img alt="Warning" src="<?=$base?>img/icons/icon_info.png">
				<span>No Client Selected.</span>
				Please select a client at the Client Search section.
			</p>
		</div>

<? else: ?>

	<!-- CONTAINER CLIENT INFO -->
		<div class="contentcontainer sml left">

			<div class="headings alt">


				<h2>
                	<?=$client->id?> -
                	<? $inputcss = 'class="noborders h2input headings"'; ?>
                	<input id='client_title' value="<?=$client->title?>" <?=$inputcss?> style="width:45px;">
                	<input id='client_name' value="<?=$client->name?>" <?=$inputcss?> style="width:60%;">
                </h2>
            </div>

			<div class="contentbox">

            	<table width="100%">
                    <tbody>
						<tr>
							<td>
								Status<br />
								<select id="client_status">
									<?=$client_statuses?>
								</select>
							</td>

							<td id="td_reference">
								Reference:<br />
								<select id="client_reference" class='medbox2'>
									<option value='0'>Select Reference...</option>
									<?=$so_references?>
								</select>
								<? $display = ($client->id_reference > 0 ) ? '':'style="display:none;"' ?>
								<input type="button" class="btn" id="ref_more" value="+" <?=$display?>
									rel="<?=$base?>ajax/ref_info/<?=$client->id_reference?>/" />
							</td>

						</tr>
						<tr>
							<td>
								Company<br />
								<strong>
									<a href="<?=$base?>companies/view/<?=$client->id_company?>" title="">
									<?=$client->company_name?>
									</a>
								</strong>
							</td>

                        	<td>
								Registry:<br />
								<strong>
									<a href="<?=$base?>companies/view/<?=$client->cid_registry?>" title="">
									<?=$client->registry_name?>
									</a>
								</strong>
							</td>
						</tr>
						<tr>
                        	<td>
								Company Agent<br />
								<strong><?=$client->company_agent_name?></strong><br />
								<?=$client->company_agent_email?><br />
								(<?=$client->company_user_name?>)
							</td>


							<td>
								Registry Agent<br />
								<strong><?=$client->registry_agent_name?></strong><br />
								<?=$client->registry_agent_email?><br />
								(<?=$client->registry_user_name?>)
							</td>


						</tr>

                    </tbody>
                </table>


			</div>

        </div>

		<!-- CONTAINER CLIENT NOTES -->
		<div class="contentcontainer med right" id="profile_tabs">

		    <div class="headings alt">

                <h2 class="left">Notes</h2>
                <ul class="smltabs">
                	<li><a href="#tab-notes">Notes</a></li>
                	<li><a href="#tab-history">History</a></li>
                </ul>
                <div id="msg_updating_client"></div>
            </div>



			<?
			//	$client->notes = trim(strip_tags(str_replace( array('</p>','<br>'),"\r\n", $client->notes )));

				$client->notes = str_replace(array("\r\n","\r","\n"),"",$client->notes);
				/*
				<textarea id="client_notes" class="wysiwyg" style="width:100%; height:600px;"><?=$client->notes?></textarea>
				*/

			?>



			<div class="contentbox" id="tab-notes">

				<h2>Summary</h2><a name="top">&nbsp;</a>


				<input id='original-notes-save' type="button" class="btn" value="Save Changes" />

				<? $can_edit_notes = in_array($current_user['userinfo']->id_group, array(1,3)); ?>

				<? if($can_edit_notes): ?>
				<textarea id="original_notes" class="ckeditor" <?=$readonly?>><? echo $client->notes."\r\n"; ?></textarea>
				<? else: ?>
				<div id="original_notes" class="inputbox divnotes resizable"><? echo $client->notes."\r\n"; ?></div>
				<? endif; ?>


				<? $docs_sent_disabled = ($can_edit_notes) ? '' : 'disabled'; ?>
				<div id="docs-sent">
					<?
						$docs_sent = array(
											'docs_sent_ref_statement' => 'REF Statement',
											'docs_sent_ref_tt' => 'REF TT',
											'docs_sent_rbsjapan_tt' => 'REF RBSJAPAN TT'
										);

						foreach($docs_sent as $doc_id => $doc_label):

							$checked = ($client->{$doc_id}) ? 'checked' : '';
							//$onchange = ($can_edit_notes) ? 'onchange="updateDocSent(this.id);"' : '';
							$input_options = "$onchange $checked $docs_sent_disabled";
					?>
							<input type="checkbox" class="check-docs-sent" name="<?=$doc_id?>" id="<?=$doc_id?>" <?=$input_options?>> <?=$doc_label?>&nbsp;

					<?	endforeach; ?>

				</div>




				<div class="manage anchor"><a href="#bottom">Bottom</a></div>
				<!-- _________________________________________ -->

				<h2>New Notes</h2>

				<textarea id='note-val'></textarea>
				<input id='note-save' type="button" class="btn" value="Add" />

				&nbsp;&nbsp;

				<input type="button" class="btn" id="quick-note-na" value="N/A">
				<input type="button" class="btn" id="quick-note-ct" value="Call Tomorrow">


				<!-- notes layout at controllers/ajax/get_notes.php -->
				<div id='notes-panel'>loading...</div>


				<div class="manage anchor"><a href="#top">Top</a></div>
				<a name="bottom">&nbsp;</a>


			</div>

			<div class="contentbox" id="tab-history">


				<table width="100%">
					<tbody>
						<tr>
							<th colspan=2>Status History</th>
						</tr>

						<? if(isset($client_history['status']) && is_array($client_history['status']) && count($client_history['status']) > 0) foreach($client_history['status'] as $status_change): ?>

						<tr>

							<td><?=$status_change->name?></td>
							<td><?=$status_change->created_at?></td>
						</tr>

						<? endforeach; ?>

						<tr>
							<th colspan=2>Agent History</th>
						</tr>

						<? if(isset($client_history['agents']) && is_array($client_history['agents']) && count($client_history['agents']) > 0) foreach($client_history['agents'] as $agent_change): ?>

						<tr>

							<td>

								<?=$agent_change->name?> (<?=$agent_change->username?>)
								<? if($agent_change->field == 'id_employee_registry'): ?><strong style='float:right;'>-- Registry --</strong><? endif?>
							</td>
							<td><?=$agent_change->created_at?></td>
						</tr>

						<? endforeach; ?>


					</tbody>
				</table>



			</div>

        </div>

		<!-- CONTAINER CONTACT INFO -->
		<div class="contentcontainer sml left">

		    <div class="headings alt">
                <h2 class="left">Contact Information</h2>
                <div class="manage"><a href="<?=$base?>client/contacts/<?=$client->id?>">Manage</a></div>
            </div>

			<div class="contentbox">

            	<table width="100%">
                    <tbody>

						<tr>
                        	<td><strong>List</strong></td>
                        	<td><?=$client->list_name?></td>
						</tr>

						<tr>
                        	<td><strong>Address</strong></td>
                        	<td><textarea rows="3" class="inputbox medbox" id="client_address"><?=$client->address?></textarea></td>
						</tr>

						<tr>
                        	<td><strong>Country</strong></td>
                        	<td><?=$client->country_name?> (<?=$client->country_iso?>)</td>
						</tr>

						<tr class='alt'>
                        	<td><strong>E-mail(s)</strong></td>
                        	<td>
								<? if(isset($emails) && is_array($emails) && count($emails) > 0) foreach($emails as $email) echo $email->contact.'<br />'; ?>
							</td>
						</tr>

						<tr>
                        	<td><strong>Contact(s)</strong></td>
                        	<td>

				            	<table width="100%">
								<? if(isset($phones) && is_array($phones) && count($phones) > 0) foreach( $phones as $phone ): ?>

									<tr>
										<td>
											<?=$phone->phone_type?>&nbsp;

											<?
												//testing id_phone_type: dont test calling code if not a custom number
												if($phone->contact_type == 'phone' && $phone->id_phone_type == 1)
												{
													$phone->contact = str_replace(' ', '', $phone->contact);
													if(substr($phone->contact, 0, 2) == '00')
														$phone->contact = substr($phone->contact,2);



													if($client->country_cc && $client->country_cc!='--')
													{
														$cc_check = substr($phone->contact, 0, strlen($client->country_cc));

														//echo( $client->country_cc . ' - '. $cc_check);

														if( $client->country_cc != $cc_check)
														{

															if(substr($phone->contact, 0, 1) == '0') $phone->contact = substr($phone->contact, 1);

															$phone->contact = $client->country_cc.$phone->contact;
														}

													}
												}
											?>
											<?=$phone->contact?>

<br><br>

											<?php
											//*
												$call_routes = array(
													'CO'  => ['name' => $client->company_name, 'id' => $client->id_company, 'r' => 1],
													'REG' => ['name' => $client->registry_name, 'id' => $client->cid_registry, 'r' => 2],
													'REF' => ['name' => $client->ref_name, 'id' => $client->id_reference, 'r' => 3]
												);

												foreach($call_routes as $route_label => $route_info){
													$route_cmd = $route_info['r'].str_pad($route_info['id'], 4, "0", STR_PAD_LEFT);
													$confirm_msg = "Are you sure you want to call {$phone->contact} as \\n{$route_info['name']}?";
													$call_cmd = ($route_info['id']) ? "if(confirm('{$confirm_msg}')) location.href = 'tel:{$route_cmd}{$phone->contact}'"
																			: "alert('No {$route_label} for this client.');";
													//printr($route_info);
												?>


													<input type="button" class="btn" onclick="<?=$call_cmd?>" value="# as <?=$route_label?>">
													<?php
												}
//*/
										 /*
											if(isset($prefixes) && is_array($prefixes) && count($prefixes) > 0) foreach( $prefixes as $prefix): ?>
												<?
													$call_cmd = "location.href = 'tel:{$phone->contact}'";
													//$call_cmd = "location.href = 'sip:#{$prefix}7{$phone->contact}'";
													//$call_cmd = "location.href = 'sip://{$prefix}{$phone->contact}'";
												?>
												<input type="button" class="btn" onclick="<?=$call_cmd?>" value="#<?=$prefix?>">
											<? endforeach;

											//*/
											 ?>
											<HR>

										</td>
									</tr>
								<? endforeach; ?>
								</table>

							</td>
						</tr>

                    </tbody>
                </table>


			</div>

        </div>




		<!-- CONTAINER CLIENT SHARES -->

		<div class="contentcontainer sml left">

		    <div class="headings alt">
                <h2 class="left">Client Shares</h2>
				<div class="manage">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<a href="<?=$base?>client/shares/<?=$client->id?>">Manage</a>
				</div>

				<div id="msg_updating_share"></div>
            </div>

			<div class="contentbox">


            	<table width="100%">
					<tbody>
						<tr>
							<th style="width:10px;">S</th>
							<th style="width:60px;">Name</th>
							<th>Qty</th>
							<th>Each</th>
							<th>Total</th>
							<th style="text-align:center;">+</th>
						</tr>

						<? if(isset($shares) && is_array($shares) && count($shares) > 0) foreach($shares as $share): ?>

						<tr>
							<? $onchange = "selectClientShare({$share->id});"; ?>

							<td style="padding:2px">
								<input
									id="share_selected_<?=$share->id?>" name="client_shares"
									onchange="<?=$onchange?>" type="radio"
									<?=($share->is_selected=='1')? 'checked':'';?>
								/>
							</td>

							<? $onchange = "updateClientShare({$share->id});"; ?>

							<td><strong><?=$share->name?></strong></td>

							<td><input value="<?=$share->shares?>" id="share_qty_<?=$share->id?>" onchange="<?=$onchange?>" class="inputbox minibox" /></td>
							<td><input value="<?=$share->price?>" id="share_price_<?=$share->id?>" onchange="<?=$onchange?>" class="inputbox minibox money" /></td>

							<td>
								<strong>
									<span class="money" id="share_total_value_<?=$share->id?>"><?=$share->total_value?></span>
								</strong>
							</td>
							<td style="padding:0px; text-align:center;">
								<input type="button" class="btn share-description" value="+"
									rel="<?=$base?>ajax/share_info/<?=$share->id?>/" />
							</td>

						</tr>

						<? endforeach; ?>

                    </tbody>
                </table>
			</div>

        </div>




		<div style="clear:both;"></div>
		<br />
		<br />


<? endif; //noclient?>
