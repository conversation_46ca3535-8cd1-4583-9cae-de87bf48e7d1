

		<? if($msg): ?>

			<div class="status info">
		    	<p class="closestatus"><a href="<?=$base?>" title="Close">x</a></p>
				<p>
					<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
					<span><?=$msg?></span>
				</p>
			</div>

		<? endif; ?>

        <!-- Content Box Start -->
		<div class="contentcontainer">

			<div class="headings alt">
                <h2>
					Client Search
					<? if($total_clients):?>
						<small><?=$clients_on_page?></small>
					<? endif;?>
				</h2>
            </div>

			<div class="contentbox">

				<form id="clientSearch" method="post" action="<?=$base?>client/search">
					<input type="hidden" name="filterSent" value="true" />


					<? $fiu = strlen($filters['id']) ? 'filter-in-use': ''; ?>
					<input type="number" class="inputbox minibox <?=$fiu?>" name="filters[id]" value="<?=$filters['id']?>" placeholder="by Id..." />


					<? $fiu = strlen($filters['client']) ? 'filter-in-use': ''; ?>
					<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[client]" value="<?=$filters['client']?>"  placeholder="by Client name..." />


					<? $fiu = strlen($filters['contact']) ? 'filter-in-use': ''; ?>
					<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[contact]" value="<?=$filters['contact']?>"
					placeholder="by Phone/E-mail..." />

					<? $fiu = strlen($filters['country']) ? 'filter-in-use': ''; ?>
					<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[country]" value="<?=$filters['country']?>" placeholder="by Country..." />

					<? $fiu = strlen($filters['company']) ? 'filter-in-use': ''; ?>
					<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[company]" value="<?=$filters['company']?>" placeholder="by Company..." />

					<? $fiu = strlen($filters['stock']) ? 'filter-in-use': ''; ?>
					<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[stock]" value="<?=$filters['stock']?>"
					placeholder="by Stock Name..." />

					<? 	if (in_array($current_user['userinfo']->id_group, array(1,3)) ): ?>

					<? $fiu = strlen($filters['id_user_agent']) ? 'filter-in-use': ''; ?>
					<select name="filters[id_user_agent]" class="<?=$fiu?>">

						<option value="" <?=($filters['id_user_agent']==='')?'selected':'';?>>by User...</option>
						<option value="0" <?=($filters['id_user_agent']==='0')?'selected':'';?>>-- Not Assigned --</option>

						<?=$so['users']?>
					</select>
					<? endif; ?>

					<? $fiu = strlen($filters['id_status']) ? 'filter-in-use': ''; ?>
					<select name="filters[id_status]" class="<?=$fiu?>">
						<option value="">by Status...</option>
						<?=$so['statuses']?>
					</select>

					<? $fiu = strlen($filters['id_list']) ? 'filter-in-use': ''; ?>
					<select name="filters[id_list]" class="<?=$fiu?>">
						<option value="">by List...</option>
						<?=$so['lists']?>
					</select>



					<input type="submit" value="Apply Filters" class="btn" />

					&nbsp;<a href="<?=$base?>client/search/clearFilters"><small>Clear Filters</small></a>

				</form>

				<br />

            	<table width="100%">
                	<thead>
                    	<tr>

	                    	<th width=20>Id</th>
	                        <th>Client</th>
	                        <th width=20>Country</th>
	                        <th>Company/Registry</th>
	                        <th>Current Agent</th>
	                        <th width=120>Current User</th>
	                        <th>Status</th>
	                        <th>List</th>

                        </tr>
                    </thead>
                    <tbody>

                    	<?php if(isset($clients) && is_array($clients) && count($clients) > 0): ?>
							<?php $i = 0; // Initialize counter for alternating row colors ?>
							<?php foreach($clients as $client):?>

							<?php
								$blue = (isset($current_user['userinfo']->id_lastLead) && $current_user['userinfo']->id_lastLead == $client->id) ? 'blue' : '';
								$grey = (isset($last_client->id) && $last_client->id == $client->id) ? 'grey' : '';
								$alt =  ( ($i++) % 2 ) ? 'alt':'';
								if( strlen($blue) || strlen($grey) ) $alt = '';
							?>

							<?php $onclick = "location.href = '{$base}client/profile/{$client->id}';"; ?>
							<tr class="<?=$alt?> <?=$blue?> <?=$grey?>" onclick="<?=$onclick?>">
								<td><?=$client->id?></td>
								<td><?=$client->name?></td>
								<td><?=$client->country_name?></td>
								<td>
									C: <?=$client->company_name?>
									<?=strlen($client->registry_name)?'<br>R: '.$client->registry_name:''?>

								</td>
								<td>
									C: <?=strlen($client->company_agent_email)?$client->company_agent_email:$client->company_agent_name?><br>
									R: <?=strlen($client->registry_agent_email)?$client->registry_agent_email:$client->registry_agent_name?>
								</td>
								<td>
									C: <?=$client->company_user_name?>
									<?=strlen($client->registry_name)?'<br>R: '.$client->registry_user_name:''?>
								</td>
								<td><?=$client->status_name?></td>
								<td><?=$client->list_name?></td>
							</tr>

							<?php endforeach; ?>

						<?php else: ?>

							<tr>
								<td colspan="8">

            						<div class="status info">
										<p>
											<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
											<span>No results found:</span>
											Make sure you have the right priveleges and all words are spelled correctly.
										</p>
									</div>

								</td>
							</tr>

						<?php endif;?>


                    </tbody>
                </table>
 				<div class="spacer"></div>

                <div class="pagination-controls">
                    <!-- Entries per page selector -->
                    <div class="entries-selector">
                        <form method="get" action="<?=$base?>client/search/1">
                            <!-- Preserve current filters -->
                            <?php if(isset($filters) && is_array($filters)): ?>
                                <?php foreach($filters as $filter_name => $filter_value): ?>
                                    <?php if(!empty($filter_value) || $filter_value === '0'): ?>
                                        <input type="hidden" name="filters[<?=$filter_name?>]" value="<?=htmlspecialchars($filter_value)?>">
                                    <?php endif; ?>
                                <?php endforeach; ?>
                                <input type="hidden" name="filterSent" value="1">
                            <?php endif; ?>

                            <div class="entries-per-page">
                                <label>Show</label>
                                <select name="rows_per_page" onchange="this.form.submit();">
                                    <?php
                                    $options = [10, 25, 50, 100, 500, 'ALL'];
                                    foreach($options as $n):
                                        $value = ($n === 'ALL') ? 999999 : $n;
                                        $display = ($n === 'ALL') ? 'ALL' : $n;
                                        $selected = ($rows_per_page == $value || ($n === 'ALL' && $rows_per_page >= 999999)) ? 'selected' : '';
                                    ?>
                                        <option value="<?=$value?>" <?=$selected?>><?=$display?></option>
                                    <?php endforeach; ?>
                                </select>
                                <label>entries</label>
                            </div>
                        </form>
                    </div>

                    <!-- Pagination buttons -->
                    <div class="pagination-buttons">
                        <ul class="pagination">
                            <?=$pagination?>
                        </ul>
                    </div>

                    <!-- Results info -->
                    <div class="results-info">
                        <span><?=$clients_on_page?></span>
                    </div>
                </div>
                <div style="clear: both;"></div>
            </div>

        </div>
        <!-- Content Box End -->
