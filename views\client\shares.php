

<? if($msg): ?>

	<div class="status info">
		<p class="closestatus"><a href="<?=$base?>client/share" title="Close">x</a></p>
		<p>
			<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
			<span><?=$msg?></span>
		</p>
	</div>

<? endif; ?>

	<!-- CONTAINER client Shares -->
	<div class="contentcontainer small left" >

	    <div class="headings alt">
	        <h2 class="left"><?=$curr_client->name?> Shares</h2>
	    </div>

		<div class="contentbox">

			<? $form_action = ($subaction == 'editshare')? "editshare/{$curr_share->id}" : 'addshare'; ?>

			<form method='post' action="<?=$base?>client/shares/<?=$form_action?>" id="form-shares">

				<input type="hidden" name="sent_share" value="true">


				<? $onchange = "calcClientShare();"; ?>
				<table>
					<tbody>
						<tr>
							<td><strong>Share Name:</strong></td>
							<td><input name="fields_share[name]" class="inputbox medbox" value="<?=$curr_share->name?>" /></td>
							<td><strong>Quantity:</strong></td>
							<td><input id="share_qty" name="fields_share[shares]" class="inputbox minibox" value="<?=$curr_share->shares?>"  onchange="<?=$onchange?>"/></td>
							<td><strong>Price:</strong></td>
							<td><input id="share_price" name="fields_share[price]" class="inputbox minibox money" value="<?=$curr_share->price ?>"  onchange="<?=$onchange?>" /></td>
							<td><strong>Total Value:</strong></td>
							<td><input id="share_total_value" name="fields_share[total_value]" type="text" class="inputbox smallbox money noborders" value="<?=$curr_share->total_value ?>" readonly=yes /></td>

						</tr>
						<tr>
							<? $bt_value = ($subaction == 'editshare') ? 'Update and Save Changes' : 'Add new share'; ?>

							<td colspan=4>
								<input type="submit" class="btn" value="<?=$bt_value?>" />

								<? if($subaction == 'editshare'): ?>

								<a class="btn" style="color:white;" href="<?="{$base}client/shares";?>">Cancel</a>
								<a class="btn" style="color:white;" href="<?="{$base}client/shares/deleteshare/$curr_share->id";?>">Delete</a>

								<? endif;?>

							</td>
						</tr>

					</tbody>
				</table>

				<br />

			</form>

	    	<table width="100%">
	        	<thead>
	            	<tr>
	                    <th>Share Name</th>
						<th>Qty</th>
						<th>Each</th>
						<th>Total</th>
					</tr>
	            </thead>
	            <tbody>

	        	<? if(isset($shares) && is_array($shares) && count($shares) > 0): ?>

					<? foreach($shares as $share):?>

					<? $onclick = "location.href = '{$base}client/shares/editshare/{$share->id}';"; ?>

					<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
						<td><?=$share->name ?></td>
						<td><?=$share->shares ?></td>
						<td class="money"><?=$share->price ?></td>
						<td class="money"><?=$share->total_value ?></td>
					</tr>

					<? endforeach; ?>

				<? else: ?>

					<tr>
						<td colspan="2">

							<div class="status info">
								<p>
									<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
									<span>No results found:</span>
									Make sure you have the right priveleges and all words are spelled correctly.
								</p>
							</div>

						</td>
					</tr>

				<? endif;?>


	            </tbody>
	        </table>

	    </div>

	</div>



<br />