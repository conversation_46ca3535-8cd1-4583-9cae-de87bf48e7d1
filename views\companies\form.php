

<? if($msg): ?>

		<div class="status info">
        	<p class="closestatus"><a href="<?=$base?>companies/manage" title="Close">x</a></p>
			<p>
				<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
				<span><?=$msg?></span>
			</p>
		</div>

<? endif; ?>


<? if($action == 'edit' && $id): ?>

	<!-- CONTAINER company employees -->
	<div class="contentcontainer med right" >

	    <div class="headings alt">
			<? $h2_value = ($subaction == 'editEmployee') ? "- Editing $employee->name" : '- Add new Employee'; ?>

	        <h2 class="left">Company People <?=$h2_value;?></h2>
	    </div>


		<div class="contentbox">
		<?	//printr($employee);?>

		<? $form_action = ($subaction == 'editEmployee')? "editEmployee/$employee->id" : 'addEmployee'; ?>

		<form method='post' action="<?=$base?>companies/edit/<?=$company->id?>/<?=$form_action?>">

			<input type="hidden" name="sent_employee" value="true">
			<input type="hidden" name="fields_employee[id_company]" value="<?=$company->id?>">

			<table width="560">
				<tbody>
					<tr>
						<td><strong>Title:</strong></td>
						<td><input name="fields_employee[title]" type="text" class="inputbox minibox" value="<?=$employee->title?>" /></td>
						<td><strong>Position:</strong></td>
						<td>
							<select name="fields_employee[id_position]">
								<?=$so['positions']?>
							</select>
						</td>

					</tr>
					<tr>

						<td><strong>Name:</strong></td>
						<td><input name="fields_employee[name]" type="text" class="inputbox medbox" value="<?=$employee->name?>" /></td>
						<td><strong>User:</strong></td>
						<td>
							<select name="fields_employee[id_user]">
								<option value='0'>Select user...</option>
								<?=$so['users']?>
							</select>
						</td>

					</tr>
					<tr>

						<td><strong>E-mail:</strong></td>
						<td><input name="fields_employee[email]" type="text" class="inputbox medbox" value="<?=$employee->email?>" /></td>
						<td><strong>Status:</strong></td>
						<td>
							<select name="fields_employee[is_active]">
								<option <?=($employee->is_active==0)?'selected':''?> value="0">Inactive</option>
								<option <?=($employee->is_active==1)?'selected':''?> value="1">Active</option>
							</select>
						</td>
					</tr>
					<tr>
						<? $bt_value = ($subaction == 'editEmployee') ? 'Update and Save Changes' : 'Add new Employee'; ?>
						<td colspan=4>
							<input type="submit" class="btn" value="<?=$bt_value?>" />

							<? if($subaction == 'editEmployee'): ?>

							<a class="btn" style="color:white;" href="<?="{$base}companies/edit/{$company->id}/addEmployee";?>">Cancel</a>
							<a class="btn" style="color:white;" href="<?="{$base}companies/edit/{$company->id}/deleteEmployee/{$employee->id}";?>">Delete</a>

							<? endif;?>

						</td>
					</tr>

				</tbody>
			</table>

			<br />

		</form>

	    	<table width="100%">
	        	<thead>
	            	<tr>
	                    <th width=100>Position</th>
	                    <th>Name</th>
	                    <th>E-mail</th>
	                    <th width=50>User</th>
	                    <th>Status</th>
	                </tr>
	            </thead>
	            <tbody>

	            	<? if(isset($employees) && is_array($employees) && count($employees) > 0): ?>

						<? foreach($employees as $employee):?>

						<? $onclick = "location.href = '{$base}companies/edit/{$company->id}/editEmployee/{$employee->id}';"; ?>

						<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
							<td><?=$employee->position ?></td>
							<td><?=$employee->title.' '.$employee->name ?></td>
							<td><?=$employee->email ?></td>
							<td><?=$employee->username ?></td>
							<td><?=($employee->is_active)?'<strong>Active</strong>':'Inactive';?></td>
						</tr>

						<? endforeach; ?>

					<? else: ?>

						<tr>
							<td colspan="8">

	    						<div class="status info">
									<p>
										<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
										<span>No results found:</span>
										Make sure you have the right priveleges and all words are spelled correctly.
									</p>
								</div>

							</td>
						</tr>

					<? endif;?>


	            </tbody>
	        </table>

	    </div>

	</div>

<? endif; ?>



<!-- CONTAINER company info -->
<div class="contentcontainer sml left">


	<div class="headings alt" id='testing'>
        <h2 class="left"><?=($action == 'add') ? 'Adding New' : 'Editing'; ?> Company/Registry</h2>
    </div>


	<div class="contentbox">

		<form method='post' action="<?=$base?>companies/<?=$action?>/<?=$company->id?>" id="form-company">
			<input type="hidden" name="sent" value="true">

			<ul>
				<li>

					<table>
						<tbody>
							<tr>
								<td><strong>Company Type:</strong></td>
								<td >
									<select name="fields[company_type]">
										<option <?=($company->company_type=='company')?'selected':''?> value="company">Company</option>
										<option <?=($company->company_type=='registry')?'selected':''?> value="registry">Registry</option>
									</select>
								</td>
							</tr>
							<tr>
								<td><strong>Name:</strong></td>
								<td><input name="fields[name]" type="text" class="inputbox medbox" value="<?=$company->name?>" /></td>
							</tr>


							<tr>
								<td><strong>Reg No.: <br>(For companies only)</strong></td>
								<td><input name="fields[short_name]" type="text" class="inputbox medbox" value="<?=$company->short_name?>" /></td>
							</tr>

							<tr>
								<td><strong>E-mail:</strong></td>
								<td><input name="fields[email]" type="text" class="inputbox medbox" value="<?=$company->email?>" /></td>
							</tr>
							<tr>
								<td><strong>Website:</strong></td>
								<td><input name="fields[site_url]" type="text" class="inputbox medbox" value="<?=$company->site_url?>" /></td>
							</tr>

							<tr>
								<td><strong>Address:</strong></td>
								<td><textarea name="fields[address]" type="text" class="inputbox medbox"><?=$company->address?></textarea></td>
							</tr>

							<tr>
								<td><strong>City:</strong></td>
								<td><textarea name="fields[city]" type="text" class="inputbox medbox"><?=$company->city?></textarea></td>
							</tr>

							<tr>
								<td><strong>Nationality:</strong></td>
								<td><textarea name="fields[nationality]" type="text" class="inputbox medbox"><?=$company->nationality?></textarea></td>
							</tr>

							<tr>
								<td><strong>Country:</strong></td>
								<td><textarea name="fields[country]" type="text" class="inputbox medbox"><?=$company->country?></textarea></td>
							</tr>

							<tr>
								<td><strong>Phone:</strong></td>
								<td><input name="fields[phone]" type="number" class="inputbox medbox" value="<?=$company->phone?>" /></td>
							</tr>

							<tr>
								<td><strong>Fax:</strong></td>
								<td><input name="fields[fax]" type="text" class="inputbox medbox" value="<?=$company->fax?>" /></td>
							</tr>

							<tr>
								<td><strong>Managing Director:</strong></td>
								<td><input name="fields[director]" type="text" class="inputbox medbox" value="<?=$company->director?>" /></td>
							</tr>

							<tr>
								<td><strong>Sr. Manager - Compliance Department:</strong></td>
								<td><input name="fields[comp_manager]" type="text" class="inputbox medbox" value="<?=$company->comp_manager?>" /></td>
							</tr>


<!--

ALTER TABLE `companies`
	ADD COLUMN `comp_manager` VARCHAR(250) NULL DEFAULT NULL AFTER `director`;
							<tr>
								<td><strong>Comission Fee:</strong></td>
								<td><input name="fields[commission_fee]" type="text" class="inputbox minibox" value="<?=$company->commission_fee?>" />%</td>
							</tr>

							<tr>
								<td><strong>Domain Expiration</strong></td>
								<td><input name="fields[domain_expiration]" type="text" class="inputbox medbox" value="<?=$company->domain_expiration ?>" /></td>
							</tr>
-->
							<tr>
								<td><strong>Status:</strong></td>
								<td>
									<select name="fields[is_active]">
										<option <?=($company->is_active=='0')?'selected':''?> value="0">Inactive</option>
										<option <?=($company->is_active=='1')?'selected':''?> value="1">Active</option>
									</select>
								</td>
							</tr>

							<? if($company->company_type == 'company'): ?>

							<tr>
								<td><strong>Related Registry:</strong></td>
								<td>
									<select name="fields[id_registry]" class="inputbox medbox">
										<option>Select Registry...</option>
										<?=$so['registries']?>
									</select>
								</td>
							</tr>

							<? endif; ?>

						</tbody>
					</table>
					<br />
					<br />

				</li>


				<li><input type="submit" class="btn" value="Save Changes" /></li>

			</ul>

		</form>

	</div>


</div>

<br />