


<!-- Companies -->
<div class="contentcontainer" id="company_tabs" >



	<div class="headings alt">
        <h2 class="left">
			Company Directory
			<? if($total_companies):?><small>(<?=$total_companies?> companies found)</small><? endif;?>
			<? if($total_registries):?><small>(<?=$total_registries?> registries found)</small><? endif;?>
		</h2>



        <ul class="smltabs">
        	<li><a href="#tab-companies">Companies</a></li>
            <li><a href="#tab-registries">Registries</a></li>
        </ul>


		<br><br><br><br>


		<form id="userSearch" method="post">

			<? //printr($filters); ?>

			<input type="hidden" name="filterSent" value="true" />

			<input type="text" class="inputbox smallbox" name="filters[name]"
				value="<?=$filters['name']?>"  placeholder="by Name..." />


			<select name="filters[is_active]">
				<option <?=(empty($filters))?'selected':''?> value="">by Status...</option>
				<option <?=($filters['is_active']=='1')?'selected':''?> value="1">Active</option>
				<option <?=($filters['is_active']=='0')?'selected':''?> value="0">Inactive</option>
			</select>

			<input type="submit" value="Apply Filters" class="btn" />

		</form>

		<br />

    </div>

	<div class="contentbox" id="tab-companies">


		<br><br><br>


    	<table width="100%">
        	<thead>
            	<tr>
                	<th>Id</th>
                    <th>Name</th>
                    <th>Address</th>
                    <th>Website</th>
                    <th>Status</th>

                </tr>
            </thead>
            <tbody>

            	<? if(isset($companies['company']) && is_array($companies['company']) && count($companies['company']) > 0): ?>

					<? foreach($companies['company'] as $company):?>

					<?
						$link_view =  (in_array($group, array(1,3) )) ? 'edit' : 'view';
						$onclick = "location.href = '{$base}companies/{$link_view}/{$company->id}';";
					?>

					<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
						<td><?=str_pad($company->id, 5, "0", STR_PAD_LEFT) ?></td>
						<td><?=$company->name ?></td>
						<td><?=$company->address ?></td>
						<td><?=$company->site_url ?></td>
						<td><?=($company->is_active)?'<strong>Active</strong>':'Inactive';?></td>
					</tr>

					<? endforeach; ?>

				<? else: ?>

					<tr>
						<td colspan="8">

    						<div class="status info">
								<p>
									<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
									<span>No results found:</span>
									Make sure you have the right priveleges and all words are spelled correctly.
								</p>
							</div>

						</td>
					</tr>

				<? endif;?>


            </tbody>
        </table>
			<div class="spacer"></div>

        <ul class="pagination">

			<?=$pagination?>

        </ul>
        <div style="clear: both;"></div>
    </div>




	<div class="contentbox" id="tab-registries">

		<br><br><br>


    	<table width="100%">
        	<thead>
            	<tr>
                	<th>Id</th>
                    <th>Name</th>
                    <th>Address</th>
                    <th>Website</th>
                    <th>Status</th>

                </tr>
            </thead>
            <tbody>

            	<? if(isset($companies['registry']) && is_array($companies['registry']) && count($companies['registry']) > 0): ?>

					<? foreach($companies['registry'] as $company):?>

					<? $onclick = "location.href = '{$base}companies/edit/{$company->id}';"; ?>

					<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">
						<td><?=str_pad($company->id, 5, "0", STR_PAD_LEFT) ?></td>
						<td><?=$company->name ?></td>
						<td><?=$company->address ?></td>
						<td><?=$company->site_url ?></td>
						<td><?=($company->is_active)?'<strong>Active</strong>':'Inactive';?></td>
					</tr>

					<? endforeach; ?>

				<? else: ?>

					<tr>
						<td colspan="8">

    						<div class="status info">
								<p>
									<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
									<span>No results found:</span>
									Make sure you have the right priveleges and all words are spelled correctly.
								</p>
							</div>

						</td>
					</tr>

				<? endif;?>


            </tbody>
        </table>
			<div class="spacer"></div>

        <ul class="pagination">

			<?=$pagination?>

        </ul>
        <div style="clear: both;"></div>
    </div>




















</div>
<!-- Content Box End -->