

<? if($msg): ?>

		<div class="status info">
        	<p class="closestatus"><a href="<?=$base?>companies/manage" title="Close">x</a></p>
			<p>
				<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
				<span><?=$msg?></span>
			</p>
		</div>

<? endif; ?>


<? if($action == 'view' && $id): ?>

	<!-- CONTAINER company employees -->
	<div class="contentcontainer med right" >

	    <div class="headings alt">

	        <h2 class="left">Company People</h2>
	    </div>


		<div class="contentbox">
		<?	//printr($employee);?>



	    	<table width="100%">
	        	<thead>
	            	<tr>
	                    <th>Position</th>
	                    <th>Name</th>
	                    <th>E-mail</th>
	                    <th>Related User</th>
	                    <th>Status</th>
	                </tr>
	            </thead>
	            <tbody>

	            	<? if(isset($employees) && is_array($employees) && count($employees) > 0): ?>

						<? foreach($employees as $employee): if($employee->is_active): ?>


						<tr <?=(($i++)%2)?"class='alt'":''?>>
							<td><?=$employee->position ?></td>
							<td><?=$employee->title.' '.$employee->name ?></td>
							<td><?=$employee->email ?></td>
							<td><?=$employee->username ?></td>
							<td><?=($employee->is_active)?'<strong>Active</strong>':'Inactive';?></td>
						</tr>

						<? endif; endforeach; ?>

					<? else: ?>

						<tr>
							<td colspan="8">

	    						<div class="status info">
									<p>
										<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
										<span>No results found:</span>
										Make sure you have the right priveleges and all words are spelled correctly.
									</p>
								</div>

							</td>
						</tr>

					<? endif;?>


	            </tbody>
	        </table>

	    </div>

	</div>

<? endif; ?>



<!-- CONTAINER company info -->
<div class="contentcontainer sml left">


	<div class="headings alt" id='testing'>
        <h2 class="left">Company/Registry</h2>
    </div>


	<div class="contentbox">


			<ul>
				<li>

					<table>
						<tbody>
							<tr>
								<td><strong>Name:</strong></td>
								<td><?=$company->name?></td>
							</tr>
							<tr>
								<td><strong>Short Name:</strong></td>
								<td><?=$company->short_name?></td>
							</tr>
							<tr>
								<td><strong>E-mail:</strong></td>
								<td><?=$company->email?></td>
							</tr>
							<tr>
								<td><strong>Website:</strong></td>
								<td><?=$company->site_url?></td>
							</tr>

							<tr>
								<td><strong>Address:</strong></td>
								<td><?=$company->address?></td>
							</tr>

							<tr>
								<td><strong>Phone:</strong></td>
								<td><?=$company->phone?></td>
							</tr>

							<tr>
								<td><strong>Fax:</strong></td>
								<td><?=$company->fax?></td>
							</tr>
<!--
							<tr>
								<td><strong>Comission Fee:</strong></td>
								<td><?=$company->commission_fee?>%</td>
							</tr>

							<tr>
								<td><strong>Domain Expiration</strong></td>
								<td><?=$company->domain_expiration ?></td>
							</tr>
-->
							<tr>
								<td><strong>Status:</strong></td>
								<td>
									<select name="fields[is_active]" disabled>
										<option <?=($company->is_active=='0')?'selected':''?> value="0">Inactive</option>
										<option <?=($company->is_active=='1')?'selected':''?> value="1">Active</option>
									</select>
								</td>
							</tr>

							<? if($company->company_type == 'company'): ?>

							<tr>
								<td><strong>Related Registry:</strong></td>
								<td>
									<select name="fields[id_registry]" class="medbox" disabled>
										<option>-- Not Assigned --</option>
										<?=$so['registries']?>
									</select>
								</td>
							</tr>

							<? endif; ?>

						</tbody>
					</table>
					<br />
					<br />

				</li>



			</ul>

	</div>


</div>

<br />