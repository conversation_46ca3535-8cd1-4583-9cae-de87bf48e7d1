        <!-- Content Box Start -->
        <div>
			<div class="contentcontainer">
				<? $onclick = "onclick=\"location.href = '{$base}maintenance/{$table}/add';\""; ?>
				<input type="button" class="btn right" <?=$onclick?> value="Add New <?=ucwords(substr($table,0,-1));?>">
                <h2>
					Manage <?=$tables[$table]['title'];?>
					<? if($total_records):?><small>(<?=$total_records?> found)</small><? endif;?>
				</h2>
            </div>

			<div class="contentbox">

				<form id="recordSearch" method="post" action="<?=$base?>maintenance/<?=$table?>">

					<? //printr($filters); ?>

					<input type="hidden" name="filterSent" value="true" />
					<input type="text" class="inputbox smallbox" name="q"
						value="<?=$q?>"  placeholder="Search..." />

					<input type="submit" value="Apply Filters" class="btn" />

				</form>

				<br />

            	<table width="100%">
                	<thead>
                    	<tr>
                    		<? foreach($table_fields_parts as $table_field): ?>
                            <th><?=$table_field?></th>
                    		<? endforeach; ?>
                    		<th></th>
                        </tr>
                    </thead>
                    <tbody>

                    	<? if(isset($records) && is_array($records) && count($records) > 0): ?>

							<? foreach($records as $r): if($r->id != 0): ?>

							<? $onclick = "location.href = '{$base}maintenance/{$table}/edit/{$r->id}';"; ?>




							<tr <?=(($i++)%2)?"class='alt'":''?> onclick="<?=$onclick?>">

        	            		<? foreach($table_fields_parts as $table_field): ?>


								<? switch($table_field)
									{

										case 'id_user':
										{
											$value = $r->{$table_field};
											$value = $db->result($db->query("SELECT username FROM users WHERE id = $value"),0,0);
											break;
										}

										case 'is_active':
										{
											$value = ($r->{$table_field} === '1') ? 'Active':'Inactive';
											break;
										}

										default:
										{

		        	            			$value = strlen( $r->{$table_field} ) ? $r->{$table_field} : ' -- ';
											break;
										}

									}
								?>

        	            		<?
        	            			$width = '';
        	            			if($table_field == 'id'){
										$width = "style='width:30px;'";
										$value = str_pad($value, 5, "0", STR_PAD_LEFT);
        	            			}
        	            			if($table_field == 'name' || $table_field == 'email' )	$width = "style='width:260px;'";

        	            		?>

    	                        <td <?=$width?>><?=$value?></td>
	                    		<? endforeach; ?>
	                    		<td></td>
							</tr>

							<? endif; endforeach; ?>

						<? else: ?>

							<tr>
								<td colspan="8">

            						<div class="status info">
										<p>
											<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
											<span>No results found:</span>
											Make sure you have the right priveleges and all words are spelled correctly.
										</p>
									</div>

								</td>
							</tr>

						<? endif;?>


                    </tbody>
                </table>
 				<div class="spacer"></div>

                <ul class="pagination">

					<?=$pagination?>

                </ul>
                <div style="clear: both;"></div>
            </div>

        </div>
        <!-- Content Box End -->