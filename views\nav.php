		<?php //printr($nav); ?>

		<ul id="nav">

			<?php foreach($nav as $section): $has_header = false; ?>
			<li>

				<?php if($section['header']): ?>

					<?php
						$header_title = key($section['header']);
						$header_link  = $section['header'][$header_title];
						$heading_state = ( $header_link == $interface ) ? 'expanded' : 'collapsed';
					?>

					<a class="<?=htmlspecialchars($heading_state)?> heading"><?=htmlspecialchars($header_title) ?></a>

				<?php endif; ?>

				<ul class="navigation">
					<?php //echo "$header_link == $interface && $option_link == $action"?>
				<?php foreach($section['options'] as $option_title => $option_link): ?>

					<?php
						$current_subsection = isset($subsection) ? $subsection : '';
						if($header_link == $interface && ($option_link == $action || $option_link == $current_subsection)):
					?>
						<li class="heading selected"><?=htmlspecialchars($option_title)?></li>
					<?php else: ?>
						<li><a href="<?="{$base}{$header_link}/{$option_link}/"?>" title="<?=htmlspecialchars($option_title)?>"><?=htmlspecialchars($option_title)?></a></li>
					<?php endif;?>

				<?php endforeach; ?>
				</ul>

			</li>
			<?php endforeach; ?>

		</ul>