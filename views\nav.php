		<? //printr($nav); ?>

		<ul id="nav">
        
			<? foreach($nav as $section): $has_header = false; ?>
			<li>	

				<? if($section['header']): ?> 
				
					<?
						$header_title = key($section['header']);
						$header_link  = $section['header'][$header_title];
						$heading_state = ( $header_link == $interface ) ? 'expanded' : 'collapsed'; 
					?>
					
					<a class="<?=$heading_state?> heading"><?=$header_title ?></a>
			
				<? endif; ?>

				<ul class="navigation">
					<? //echo "$header_link == $interface && $option_link == $action"?>
				<? foreach($section['options'] as $option_title => $option_link): ?>
	
					<? if($header_link == $interface && ($option_link == $action || $option_link == isset(isset($subsection) ? $subsection : '') ? isset($subsection) ? $subsection : '' : '')):  ?>
						<li class="heading selected"><?=$option_title?></li>
					<? else: ?>							
						<li><a href="<?="{$base}{$header_link}/{$option_link}/"?>" title="<?=$option_title?>"><?=$option_title?></a></li>
					<? endif;?>
    			
				<? endforeach; ?>
				</ul>
	
			</li>
			<? endforeach; ?>

		</ul>