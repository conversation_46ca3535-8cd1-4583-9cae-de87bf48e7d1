<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>CRMv2</title>
<link href="<?=$base?>styles/blue.css" rel="stylesheet" type="text/css" />
<link href="<?=$base?>styles/layout.css" rel="stylesheet" type="text/css" />
<link href="<?=$base?>_lib/js/cluetip/jquery.cluetip.css" rel="stylesheet" type="text/css" />
<link href="<?=$base?>_lib/js/jqueryui_css/jquery.ui.all.css" rel="stylesheet" type="text/css" />
</head>
<body id="homepage">


<?php if(!isset($pdf) || !$pdf): ?>
	<div id="header">
		<a href="" title=""><img src="<?=$base?>img/cp_logo.png" alt="CRMv2" class="logo" /></a>

		<?php if(isset($action) && $action == 'profile'):?>
		<div class="right" style="padding:20px;">

			<?php if(isset($client_nav) && $client_nav->previous_id):
					$onclick = "location.href = '{$base}client/profile/{$client_nav->previous_id}';"; ?>
					<input type="button" class="btn" onclick="<?=$onclick?>" value="Previous">
			<?php endif; ?>

			<?php if(isset($client_nav) && $client_nav->next_id):
					$onclick = "location.href = '{$base}client/profile/{$client_nav->next_id}';"; ?>
					<input type="button" class="btn" onclick="<?=$onclick?>" value="Next">
			<?php endif; ?>


		</div>
		<?php endif; ?>
	</div>


	<!-- Left Dark Bar Start -->
	<div id="leftside">

		<div class="user">

			<img src="<?=$base?>img/avatar.png" width="44" height="44" class="hoverimg" alt="Avatar" />

			<p>Logged in as:</p>
			<p class="username"><?=$current_user['userinfo']->username?></p>
	<!--    <p class="userbtn"><a href="<?=$base?>user/profile" title="">Profile</a></p>	-->
			<p class="userbtn"><a href="<?=$base?>logout" title="">Log out</a></p>
		</div>

		<?php
			include("controllers/nav.php");
			include("views/nav.php");
		?>


	</div>
	<!-- Left Dark Bar End -->



	<?php if(isset($curr_client) && $curr_client): ?>
	<!-- Info Bar Start -->
	<div id="breadcrumb">
		<ul>
			<li><img src="<?=$base?>img/icons/icon_info.png" alt="Notice" /></li>
			<li><strong>Currently Working on:</strong></li>
			<li><a href="<?=$base?>companies/view/<?=intval($curr_client->id_company)?>" title=""><?=htmlspecialchars($curr_client->company_name)?></a></li>
			<li>/</li>
			<li><a href="<?=$base?>client/profile/<?=intval($curr_client->id)?>" title=""><?=htmlspecialchars($curr_client->name)?></a></li>
		</ul>


		<?php if(isset($_SESSION['last_client']) && $_SESSION['last_client']): $last_client = $_SESSION['last_client'] ?>
		<ul class="right" style="margin-right:20px">
			<li><img src="<?=$base?>img/icons/icon_bullet.png" alt="Notice" /></li>
			<li><strong>Last Lead was:</strong></li>
			<li><a href="<?=$base?>companies/view/<?=intval($last_client->id_company)?>" title=""><?=htmlspecialchars($last_client->company_name)?></a></li>
			<li>/</li>
			<li><a href="<?=$base?>client/profile/<?=intval($last_client->id)?>" title=""><?=htmlspecialchars($last_client->name)?></a></li>
		</ul>
		<?php endif; ?>



	</div>
	<!-- Info Bar End -->
	<?php endif; ?>


<?php endif; ?>

	<!-- Right Side/Main Content Start -->
	<div id="rightside">
		<?php if(isset($view) && is_file("views/$view.php")===true) include("views/$view.php"); ?>
	</div>
   <!-- Right Side/Main Content End -->


	<script type='text/javascript' src='<?=$base?>_lib/js/jquery.min.js'></script>
	<script type='text/javascript' src='<?=$base?>_lib/js/jquery-ui.min.js'></script>
	<script type="text/javascript" src='<?=$base?>_lib/js/jquery.placeholder.min.js'></script>
	<script type="text/javascript" src='<?=$base?>_lib/js/jquery.validate.min.js'></script>
	<script type="text/javascript" src='<?=$base?>_lib/js/ckeditor/ckeditor.js' charset="utf-8"></script>
	<script type="text/javascript" src='<?=$base?>_lib/js/cluetip/jquery.cluetip.js'></script>
	<script type="text/javascript" src='<?=$base?>_lib/js/jquery.formatCurrency.min.js'></script>

	<?php include("controllers/js.php");	?>
	<script type="text/javascript" src='<?=$base?>_lib/js/functions.js'></script>



</body>
</html>
