

<? if($msg): ?>

	<div class="status info">
    	<p class="closestatus"><a href="<?=$base?>tools/change" title="Close">x</a></p>
		<p>
			<img alt="Notice" src="<?=$base?>img/icons/icon_info.png">
			<span><?=$msg?></span>
		</p>
	</div>

<? endif; ?>

	<!-- Content Box Start -->
	<div class="contentcontainer">

		<div class="headings alt">
	        <h2>
				Select Leads for Change
				<? if($total_clients):?>

					<small>(<?=$total_clients?> results found - Page <?=$page?> of <?=$total_pages?>) </small>

				<? endif;?>
			</h2>
	    </div>

		<div class="contentbox">

			<form id="clientSearch" method="post" action="<?=$base?>tools/change">

				<input type="hidden" name="filterSent" value="true" />


				<? $fiu = strlen($filters['id']) ? 'filter-in-use': ''; ?>
				<input type="text" class="inputbox minibox <?=$fiu?>" name="filters[id]" value="<?=$filters['id']?>" placeholder="by Id..." />

				<? $fiu = strlen($filters['client']) ? 'filter-in-use': ''; ?>
				<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[client]" value="<?=$filters['client']?>"  placeholder="by Client name..." />

				<? $fiu = strlen($filters['country']) ? 'filter-in-use': ''; ?>
				<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[country]" value="<?=$filters['country']?>" placeholder="by Country..." />

				<? $fiu = strlen($filters['company']) ? 'filter-in-use': ''; ?>
				<input type="text" class="inputbox smallbox <?=$fiu?>" name="filters[company]" value="<?=$filters['company']?>" placeholder="by Company..." />

				<? $fiu = strlen($filters['stock']) ? 'filter-in-use': ''; ?>
				<select name="filters[stock]" class="<?=$fiu?>">
					<option value="">by Stock...</option>

					<?php $selected = ($filters['stock'] == 'no_stock') ? 'selected':''; ?>
					<option value="no_stock" <?=$selected?>>-- No stocks --</option>

					<?php foreach($so['stocks'] as $f_name => $f_label): $selected = ($f_name == $filters['stock']) ? 'selected':''; ?>
					<option value="<?=$f_name?>" <?=$selected?>><?=$f_label?></option>
					<?php endforeach; ?>
				</select>




				<? $fiu = strlen($filters['id_user_agent']) ? 'filter-in-use': ''; ?>
				<select name="filters[id_user_agent]" class="<?=$fiu?>">


					<option value="" <?=($filters['id_user_agent']==='')?'selected':'';?>>by User...</option>
					<option value="0" <?=($filters['id_user_agent']==='0')?'selected':'';?>>-- Not Assigned --</option>
					<?=$so['users']?>
				</select>

				<!--
				<select name="filters[id_user_loader]">
					<option value="">by Loader...</option>
					<?=$so['loaders']?>
				</select>
				-->

				<? $fiu = strlen($filters['id_status']) ? 'filter-in-use': ''; ?>
				<select name="filters[id_status]" class="<?=$fiu?>">
					<option value="">by Status...</option>
					<?=$so['statuses']?>
				</select>


				<? $fiu = strlen($filters['id_list']) ? 'filter-in-use': ''; ?>
				<select name="filters[id_list]" class="<?=$fiu?>">
					<option value="">by List...</option>
					<?=$so['lists']?>
				</select>


				<? $fiu = strlen($filters['sort_type']) ? 'filter-in-use': ''; ?>
				<select name="filters[sort_type]" class="<?=$fiu?>">
					<option value="">Sort by...</option>
					<?php foreach($so['sort_types'] as $f_name => $f_label): $selected = ($f_name == $filters['sort_type']) ? 'selected':''; ?>
					<option value="<?=$f_name?>" <?=$selected?>><?=$f_label?></option>
					<?php endforeach; ?>
				</select>

				<input type="submit" value="Apply Filters" class="btn" />

			</form>

			<br />


	    	<table width="100%">
	        	<thead>
	            	<tr>
                    	<th style="background-color#FFF; padding:2px; width:10px">#</th>
						<th width=10><input type="checkbox" id="selectAll" /></th>
                    	<th width=20>Id</th>
                        <th>Client</th>
                        <th width=20>Country</th>
                        <th>Company/Registry</th>
                        <th>Current Agent</th>
                        <th width=120>Current User</th>
                        <th>Status</th>
                        <th>List</th>
	                </tr>
	            </thead>
	            <tbody>

            	<? if(isset($clients) && is_array($clients) && count($clients) > 0): $i=0;?>

					<? foreach($clients as $client):?>

					<tr <?=(($i++)%2)?"class='alt'":''?>>
						<td  style="background-color#FFF; padding:2px; width:10px"><small style="color:#AAA">#<?=$i?></small></td>
						<td><input type="checkbox" value="<?=$client->id?>" class="leads" /></td>
						<td><?=$client->id?></td>
						<td><a href="<?=$base?>client/profile/<?=$client->id?>"><?=$client->name?></a></td>
						<td><?=$client->country_name?></td>
						<td>
							C: <?=$client->company_name?>
							<?=strlen($client->registry_name)?'<br>R: '.$client->registry_name:''?>

						</td>
						<td>
							C: <?=strlen($client->company_agent_email)?$client->company_agent_email:$client->company_agent_name?><br>
							R: <?=strlen($client->registry_agent_email)?$client->registry_agent_email:$client->registry_agent_name?>
						</td>
						<td>
							C: <?=$client->company_user_name?>
							<?=strlen($client->registry_name)?'<br>R: '.$client->registry_user_name:''?>
						</td>
						<td><?=$client->status_name ?></td>
						<td><?=$client->id_list?></td>
					</tr>

					<? endforeach; ?>

				<? else: ?>

					<tr>
						<td colspan="8">

    						<div class="status info">
								<p>
									<img alt="Information" src="<?=$base?>img/icons/icon_info.png">
									<span>No results found:</span>
									Make sure all words are spelled correctly or try different keywords.
								</p>
							</div>

						</td>
					</tr>

				<? endif;?>


	            </tbody>
	        </table>


			<div class="spacer"></div>

	        <ul class="pagination">

				<?=$pagination?>

	        </ul>

	        <div style="clear: both;"></div>

	    </div>
	</div>

	<!-- Content Box End -->

	<br><br><br>

	<div class="contentcontainer" id="doc_changeleads" >


		<div class="headings alt">
	        <h2 class="left">Change Selected Leads</h2>
	        <ul class="smltabs">
	        	<li><a href="#tab-distribute">Assign to Agent</a></li>
	            <li><a href="#tab-status">Change Status</a></li>
	            <li><a href="#tab-unassign">Unassign Leads</a></li>
	            <li><a href="#tab-lists">Change List</a></li>
	            <!--<li><a href="#tab-cancel">Cancel Leads</a></li>-->
	        </ul>
	    </div>


		<div class="contentbox" id="tab-distribute">


			<form id="distributeLeads" method="post" action="<?=$base?>tools/change/<?=$var2?>">

				<input type="hidden" name="leadsSent" value="true" />
				<input type="hidden" id="selectedLeads" name="selectedLeads" value="" />


		    	<table>
					<tbody>
						<tr>
							<th>User</th>
							<th>Company / Registry</th>
							<th>Agent</th>
							<th>Action</th>
						</tr>
						<tr>
							<td>
								<select name="id_user" id="distribute-change-user" class="inputbox medbox">
									<option value="0">Select user...</option>
									<?=$so['loaders']?>
									<?=$so['users']?>
								</select>
							</td>

							<td>
								<select name="id_company" id="distribute-change-company" class="inputbox">
									<option value="0">You must select an user first.</option>
								</select>
							</td>

							<td>
								<select name="id_employee" id="distribute-change-agent" class="inputbox">
									<option value="0">You must select a company first.</option>
								</select>
							</td>
							<td>
								<input type="submit" class="btn" value="Assign Selected Leads" />
							</td>

						</tr>
		            </tbody>
		        </table>

	    	</form>

		</div>



		<div class="contentbox" id="tab-status">

			<form id="updateStatusLeads" method="post" action="<?=$base?>tools/change/<?=$var2?>">

				<input type="hidden" name="updateStatus" value="true" />
				<input type="hidden" id="updateSelected" name="updateSelected" value="" />

		    	<table width="350">
					<tbody>
						<tr>
							<th>Status</th>
							<th>Action</th>
						</tr>
						<tr>
							<td>

								<select name="new-status"  id="update-status-select">
									<option value="">select Status...</option>
									<option value="0">-- Unassigned --</option>
									<?=$so['statuses']?>

								</select>
							</td>

							<td>
								<input type="submit" class="btn" value="Apply to Selected Leads" />
							</td>

						</tr>
		            </tbody>
		        </table>

		     </form>

		</div>



		<div class="contentbox" id="tab-unassign">

			<form id="unassignLeads" method="post" action="<?=$base?>tools/change/<?=$var2?>">

				<input type="hidden" name="unassignLeads" value="true" />
				<input type="hidden" id="unassignSelected" name="unassignSelected" value="" />

		    	<table width="350">
					<tbody>
						<tr>
							<td>
								<input type="submit" class="btn" value="Unassign User from Selected Leads" />
							</td>

						</tr>
		            </tbody>
		        </table>

		     </form>

		</div>





		<div class="contentbox" id="tab-lists">

			<form id="updateLeadsList" method="post" action="<?=$base?>tools/change">

				<input type="hidden" name="updateList" value="true" />
				<input type="hidden" id="updateListSelected" name="updateListSelected" value="" />

		    	<table width="350">
					<tbody>
						<tr>
							<th>List</th>
							<th>Action</th>
						</tr>
						<tr>
							<td>

								<select name="new-list"  id="update-list-select">
									<option value="0">select List...</option>
									<?=$so['lists']?>

								</select>
							</td>

							<td>
								<input type="submit" class="btn" value="Apply to Selected Leads" />
							</td>

						</tr>
		            </tbody>
		        </table>

		     </form>

		</div>


		<br><br><br>
		<br><br><br>

	</div>




